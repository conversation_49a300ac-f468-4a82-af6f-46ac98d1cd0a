/** @type {import('tailwindcss').Config} */

const tailwindConfig = {
  content: ['./public/**/*.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  darkMode: 'class',
  theme: {
    extend: {
      animation: {
        'spin-slow': 'spin 1.3s linear infinite',
        'ping-slow': 'ping 1.3s cubic-bezier(0, 0, 0.2, 1) infinite',
        'pulse-slow': 'pulse 2.6s cubic-bezier(0.4, 0, 0.6, 1) infinite;'
      },
      colors: {
        primary: 'rgb(var(--color-primary) / <alpha-value>)',
        secondary: 'rgb(var(--color-secondary) / <alpha-value>)',
        'primary-light': 'rgb(var(--color-primary-light) / <alpha-value>)',
        'background-color': 'var(--color-background)'
      }
    }
  }
}

export default tailwindConfig
