/** @type {import('tailwindcss').Config} */

const tailwindConfig = {
  content: ['./public/**/*.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  darkMode: 'class',
  theme: {
    extend: {
      animation: {
        'spin-slow': 'spin 1.3s linear infinite',
        'ping-slow': 'ping 1.3s cubic-bezier(0, 0, 0.2, 1) infinite',
        'pulse-slow': 'pulse 2.6s cubic-bezier(0.4, 0, 0.6, 1) infinite;'
      },
      colors: {
        primary: 'rgb(var(--color-primary) / <alpha-value>)',
        secondary: 'rgb(var(--color-secondary) / <alpha-value>)',
        'primary-light': 'rgb(var(--color-primary-light) / <alpha-value>)',
        'background-color': 'var(--color-background)',
        // Afegim més colors personalitzats
        'gray-light': 'var(--color-gray-light)',
        'gray-super-light': 'var(--color-gray-super-light)',
        error: 'var(--color-error)',
        'error-light': 'var(--color-error-light)',
        warning: 'var(--color-warning)',
        success: 'var(--color-success)',
        'success-light': 'var(--color-success-light)',
        info: 'var(--color-info)',
        dark: 'var(--color-dark)',
        'dark-light': 'var(--color-dark-light)',
        'dark-hover': 'var(--color-dark-hover)',
        'dark-gray-light': 'var(--color-dark-gray-light)',
        'dark-gray': 'var(--color-dark-gray)',
        'gray-dark': 'var(--color-gray-dark)'
      }
    }
  }
}

export default tailwindConfig
