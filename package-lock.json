{"name": "parlem-user-management", "version": "0.0.1", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "parlem-user-management", "version": "0.0.1", "dependencies": {"@ag-grid-community/styles": "^32.3.5", "@azure/core-http": "^3.0.5", "@azure/msal-browser": "^4.13.0", "@azure/storage-blob": "^12.27.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "ag-grid-vue3": "^33.3.2", "axios": "^1.9.0", "parlem-webcomponents-common": "^1.1.242", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-i18n": "^11.1.5", "vue-router": "^4.5.0", "vue-toast-notification": "^3.1.3"}, "devDependencies": {"@tailwindcss/vite": "^4.1.10", "@tsconfig/node22": "^22.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitest/eslint-plugin": "^1.1.39", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "cypress": "^14.2.1", "eslint": "^9.22.0", "eslint-plugin-cypress": "^4.2.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "start-server-and-test": "^2.0.11", "tailwindcss": "^4.1.10", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}}, "node_modules/@ag-grid-community/styles": {"version": "32.3.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@ag-grid-community/styles/-/styles-32.3.5.tgz", "integrity": "sha1-R+sBuWaPfNCGd6Ia+5lnCSKR7II=", "license": "MIT"}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@antfu/utils": {"version": "0.7.10", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@asamuzakjp/css-color": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"@csstools/css-calc": "^2.1.3", "@csstools/css-color-parser": "^3.0.9", "@csstools/css-parser-algorithms": "^3.0.4", "@csstools/css-tokenizer": "^3.0.3", "lru-cache": "^10.4.3"}}, "node_modules/@aw-web-design/x-default-browser": {"version": "1.4.126", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@aw-web-design/x-default-browser/-/x-default-browser-1.4.126.tgz", "integrity": "sha1-Q+S9jwMU7ZB6hxjX6GKiA695vBY=", "license": "MIT", "dependencies": {"default-browser-id": "3.0.0"}, "bin": {"x-default-browser": "bin/x-default-browser.js"}}, "node_modules/@aw-web-design/x-default-browser/node_modules/default-browser-id": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/default-browser-id/-/default-browser-id-3.0.0.tgz", "integrity": "sha1-vue7vvH0510x+Y9NPxVWoUzqeQw=", "license": "MIT", "dependencies": {"bplist-parser": "^0.2.0", "untildify": "^4.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@azure/abort-controller": {"version": "2.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/abort-controller/-/abort-controller-2.1.2.tgz", "integrity": "sha1-Qv4MyrI4QdmQWBLFjxCC0neEVm0=", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-auth": {"version": "1.9.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/core-auth/-/core-auth-1.9.0.tgz", "integrity": "sha1-rHJbA/q+PIkjcQZe6eIEG+4P0aw=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-util": "^1.11.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-client": {"version": "1.9.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/core-client/-/core-client-1.9.4.tgz", "integrity": "sha1-u5u4Xtx4D8ZWMLbY/6Fyw2M8qP4=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-auth": "^1.4.0", "@azure/core-rest-pipeline": "^1.20.0", "@azure/core-tracing": "^1.0.0", "@azure/core-util": "^1.6.1", "@azure/logger": "^1.0.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-http": {"version": "3.0.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/core-http/-/core-http-3.0.5.tgz", "integrity": "sha1-cn/CO/2mfNENDq86MM+zoy+PtqA=", "deprecated": "This package is no longer supported. Please refer to https://github.com/Azure/azure-sdk-for-js/blob/490ce4dfc5b98ba290dee3b33a6d0876c5f138e2/sdk/core/README.md", "license": "MIT", "dependencies": {"@azure/abort-controller": "^1.0.0", "@azure/core-auth": "^1.3.0", "@azure/core-tracing": "1.0.0-preview.13", "@azure/core-util": "^1.1.1", "@azure/logger": "^1.0.0", "@types/node-fetch": "^2.5.0", "@types/tunnel": "^0.0.3", "form-data": "^4.0.0", "node-fetch": "^2.6.7", "process": "^0.11.10", "tslib": "^2.2.0", "tunnel": "^0.0.6", "uuid": "^8.3.0", "xml2js": "^0.5.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-http-compat": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/core-http-compat/-/core-http-compat-2.3.0.tgz", "integrity": "sha1-6dOWKZIR50IwiCdnQILBO9Y4xr8=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-client": "^1.3.0", "@azure/core-rest-pipeline": "^1.20.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-http/node_modules/@azure/abort-controller": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/abort-controller/-/abort-controller-1.1.0.tgz", "integrity": "sha1-eI7nhFelWvihrTQqyxgjg9IRkkk=", "license": "MIT", "dependencies": {"tslib": "^2.2.0"}, "engines": {"node": ">=12.0.0"}}, "node_modules/@azure/core-http/node_modules/@azure/core-tracing": {"version": "1.0.0-preview.13", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/core-tracing/-/core-tracing-1.0.0-preview.13.tgz", "integrity": "sha1-VYg9QK4gQvbx4SsX3QwNNMU21kQ=", "license": "MIT", "dependencies": {"@opentelemetry/api": "^1.0.1", "tslib": "^2.2.0"}, "engines": {"node": ">=12.0.0"}}, "node_modules/@azure/core-lro": {"version": "2.7.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/core-lro/-/core-lro-2.7.2.tgz", "integrity": "sha1-eHEFAnog5Fx3ZRqYsBpNOwG3Wgg=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-util": "^1.2.0", "@azure/logger": "^1.0.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-paging": {"version": "1.6.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/core-paging/-/core-paging-1.6.2.tgz", "integrity": "sha1-QNOGDcLffykdZjULLP2RcVJkM+c=", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-rest-pipeline": {"version": "1.20.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/core-rest-pipeline/-/core-rest-pipeline-1.20.0.tgz", "integrity": "sha1-kW2NbJz/a1VvCwv9W5I1JtWQ4tk=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-auth": "^1.8.0", "@azure/core-tracing": "^1.0.1", "@azure/core-util": "^1.11.0", "@azure/logger": "^1.0.0", "@typespec/ts-http-runtime": "^0.2.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-tracing": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/core-tracing/-/core-tracing-1.2.0.tgz", "integrity": "sha1-e+XVPDUi1jnPGQQsvNsZ9xvDWrI=", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-util": {"version": "1.12.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/core-util/-/core-util-1.12.0.tgz", "integrity": "sha1-C4woN+bWfD+66uIN80zwf2azSA0=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.0.0", "@typespec/ts-http-runtime": "^0.2.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/core-xml": {"version": "1.4.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/core-xml/-/core-xml-1.4.5.tgz", "integrity": "sha1-br/6hgeZy2V/DKY6WZLTWdSqSy0=", "license": "MIT", "dependencies": {"fast-xml-parser": "^5.0.7", "tslib": "^2.8.1"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/logger": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/logger/-/logger-1.2.0.tgz", "integrity": "sha1-p5rvzdV9KpZgP6tZyaZuDZAipWQ=", "license": "MIT", "dependencies": {"@typespec/ts-http-runtime": "^0.2.2", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@azure/msal-browser": {"version": "4.13.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/msal-browser/-/msal-browser-4.13.0.tgz", "integrity": "sha1-qHd/q1VURDNYG1Ldf4bj3hUy3eY=", "license": "MIT", "dependencies": {"@azure/msal-common": "15.7.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/@azure/msal-common": {"version": "15.7.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/msal-common/-/msal-common-15.7.0.tgz", "integrity": "sha1-A4MwWPwh4W9d3gVA6+YjPf3Q3Ss=", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/@azure/storage-blob": {"version": "12.27.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/storage-blob/-/storage-blob-12.27.0.tgz", "integrity": "sha1-MGKTBBEXOihGi9OA4K0sYyjXKIo=", "license": "MIT", "dependencies": {"@azure/abort-controller": "^2.1.2", "@azure/core-auth": "^1.4.0", "@azure/core-client": "^1.6.2", "@azure/core-http-compat": "^2.0.0", "@azure/core-lro": "^2.2.0", "@azure/core-paging": "^1.1.1", "@azure/core-rest-pipeline": "^1.10.1", "@azure/core-tracing": "^1.1.2", "@azure/core-util": "^1.6.1", "@azure/core-xml": "^1.4.3", "@azure/logger": "^1.0.0", "events": "^3.0.0", "tslib": "^2.2.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.27.3", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.27.4", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.27.3", "@babel/helpers": "^7.27.4", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/traverse": "^7.27.4", "@babel/types": "^7.27.3", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/core/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/generator": {"version": "7.27.3", "license": "MIT", "dependencies": {"@babel/parser": "^7.27.3", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-annotate-as-pure": {"version": "7.27.3", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.27.3"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets/node_modules/lru-cache": {"version": "5.1.1", "dev": true, "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/@babel/helper-compilation-targets/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-create-class-features-plugin": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/traverse": "^7.27.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-create-class-features-plugin/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-member-expression-to-functions": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.27.3", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-optimise-call-expression": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-replace-supers": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-skip-transparent-expression-wrappers": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.27.1", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.27.4", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.27.3"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@babel/parser/-/parser-7.28.0.tgz", "integrity": "sha1-l5gp+6tRop4TkB5agHE9vLhAgl4=", "license": "MIT", "dependencies": {"@babel/types": "^7.28.0"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-proposal-decorators": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-decorators": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-decorators": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-attributes": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-meta": {"version": "7.10.4", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-jsx": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-typescript": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-typescript": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/plugin-syntax-typescript": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/template": {"version": "7.27.2", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.27.4", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse/node_modules/globals": {"version": "11.12.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/types": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@babel/types/-/types-7.28.0.tgz", "integrity": "sha1-L9AVmm3HNTkzkgxDE2M1qbJk2VA=", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@csstools/color-helpers": {"version": "5.0.2", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT-0", "engines": {"node": ">=18"}}, "node_modules/@csstools/css-calc": {"version": "2.1.4", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "engines": {"node": ">=18"}, "peerDependencies": {"@csstools/css-parser-algorithms": "^3.0.5", "@csstools/css-tokenizer": "^3.0.4"}}, "node_modules/@csstools/css-color-parser": {"version": "3.0.10", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "dependencies": {"@csstools/color-helpers": "^5.0.2", "@csstools/css-calc": "^2.1.4"}, "engines": {"node": ">=18"}, "peerDependencies": {"@csstools/css-parser-algorithms": "^3.0.5", "@csstools/css-tokenizer": "^3.0.4"}}, "node_modules/@csstools/css-parser-algorithms": {"version": "3.0.5", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "engines": {"node": ">=18"}, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.4"}}, "node_modules/@csstools/css-tokenizer": {"version": "3.0.4", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/@csstools/selector-specificity": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@csstools/selector-specificity/-/selector-specificity-2.2.0.tgz", "integrity": "sha1-LLz4Ir83ZMlljE0uVovQwMt0gBY=", "license": "CC0-1.0", "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss-selector-parser": "^6.0.10"}}, "node_modules/@cypress/request": {"version": "3.0.8", "dev": true, "license": "Apache-2.0", "dependencies": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~4.0.0", "http-signature": "~1.4.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "performance-now": "^2.1.0", "qs": "6.14.0", "safe-buffer": "^5.1.2", "tough-cookie": "^5.0.0", "tunnel-agent": "^0.6.0", "uuid": "^8.3.2"}, "engines": {"node": ">= 6"}}, "node_modules/@cypress/xvfb": {"version": "1.2.4", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.1.0", "lodash.once": "^4.1.1"}}, "node_modules/@cypress/xvfb/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/@discoveryjs/json-ext": {"version": "0.5.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@discoveryjs/json-ext/-/json-ext-0.5.7.tgz", "integrity": "sha1-HVcr+74Ut3BOC6Dzm3SBW4SHDXA=", "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/@esbuild/aix-ppc64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/aix-ppc64/-/aix-ppc64-0.25.5.tgz", "integrity": "sha1-Tg+Rd2wrNA51VY9gVSGV9vrQnxg=", "cpu": ["ppc64"], "license": "MIT", "optional": true, "os": ["aix"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-arm": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/android-arm/-/android-arm-0.18.20.tgz", "integrity": "sha1-/tsmW8OlichMwR+BCATyNJR8NoI=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/android-arm64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/android-arm64/-/android-arm64-0.18.20.tgz", "integrity": "sha1-mEtPnI0Dd0Q8wt/O8mbQIkRZNiI=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/android-x64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/android-x64/-/android-x64-0.18.20.tgz", "integrity": "sha1-Nc9BnEz8i6voiT0pbNmQ6en3VvI=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/darwin-arm64": {"version": "0.25.5", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-x64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/darwin-x64/-/darwin-x64-0.18.20.tgz", "integrity": "sha1-1w1XkNi/R1VWtn0Pi3xb3/BT2F0=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/freebsd-arm64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.20.tgz", "integrity": "sha1-mHVc0ScH+T8hDiSU1qS1G5aXf1Q=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/freebsd-x64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/freebsd-x64/-/freebsd-x64-0.18.20.tgz", "integrity": "sha1-wesr/wORX4fCnOzkwaf6H0I7Bm4=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-arm": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-arm/-/linux-arm-0.18.20.tgz", "integrity": "sha1-PmF8YfM1CKJxUO5BdUPIq1rMc7A=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-arm64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-arm64/-/linux-arm64-0.18.20.tgz", "integrity": "sha1-utQji9j0/CW1oCEoDHcKtfw6AqA=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-ia32": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-ia32/-/linux-ia32-0.18.20.tgz", "integrity": "sha1-aZORzMupruYBm3+YkuuZIZ8VcKc=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-loong64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-loong64/-/linux-loong64-0.18.20.tgz", "integrity": "sha1-5vzLeqwXjdL/uYYEZayJ1/I7l30=", "cpu": ["loong64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-mips64el": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-mips64el/-/linux-mips64el-0.18.20.tgz", "integrity": "sha1-7v86k33pwjEN4wYiqVetG9kYMjE=", "cpu": ["mips64el"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-ppc64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-ppc64/-/linux-ppc64-0.18.20.tgz", "integrity": "sha1-L3FWveILAVJ5k+aIFDWtebqVmfs=", "cpu": ["ppc64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-riscv64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-riscv64/-/linux-riscv64-0.18.20.tgz", "integrity": "sha1-Zig4nyEBI9i0dDBFr4yqfU3fx6Y=", "cpu": ["riscv64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-s390x": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-s390x/-/linux-s390x-0.18.20.tgz", "integrity": "sha1-JV6B+yibEBAmExhYq5n7pj3PAHE=", "cpu": ["s390x"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-x64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-x64/-/linux-x64-0.18.20.tgz", "integrity": "sha1-x2kLNBevMYqbb5bfMDGohlF20zg=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/netbsd-arm64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.5.tgz", "integrity": "sha1-U7TfuP4c7pN3fJ42aJO9Paprpj0=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/netbsd-x64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/netbsd-x64/-/netbsd-x64-0.18.20.tgz", "integrity": "sha1-MOjNij3e1jl14t8kOMoQlgHr4NE=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/openbsd-arm64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.5.tgz", "integrity": "sha1-Knlsh8ROjeeAAdgIx32UiiHsIv0=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openbsd-x64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/openbsd-x64/-/openbsd-x64-0.18.20.tgz", "integrity": "sha1-eBKvMbIFBVh0yAguqc+asNpiF64=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/sunos-x64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/sunos-x64/-/sunos-x64-0.18.20.tgz", "integrity": "sha1-1cJ1w7TnPJsOzTjRymLAIPiHq50=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["sunos"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/win32-arm64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/win32-arm64/-/win32-arm64-0.18.20.tgz", "integrity": "sha1-c7x/Wp+Kd4BfNX+rl/KQ0OSCCsk=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/win32-ia32": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/win32-ia32/-/win32-ia32-0.18.20.tgz", "integrity": "sha1-7JPL8O8QhcwS5x4NZh0gVp/0IQI=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/win32-x64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/win32-x64/-/win32-x64-0.18.20.tgz", "integrity": "sha1-eGxfQfBDsHr7GvN2g9fDNmiFj20=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.7.0", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/regexpp": {"version": "4.12.1", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/config-array": {"version": "0.20.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@eslint/object-schema": "^2.1.6", "debug": "^4.3.1", "minimatch": "^3.1.2"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/config-array/node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@eslint/config-array/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@eslint/config-helpers": {"version": "0.2.2", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/core": {"version": "0.14.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/eslintrc": {"version": "3.3.1", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^10.0.1", "globals": "^14.0.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@eslint/eslintrc/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@eslint/js": {"version": "9.28.0", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}}, "node_modules/@eslint/object-schema": {"version": "2.1.6", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/plugin-kit": {"version": "0.3.1", "dev": true, "license": "Apache-2.0", "dependencies": {"@eslint/core": "^0.14.0", "levn": "^0.4.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@fal-works/esbuild-plugin-global-externals": {"version": "2.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@fal-works/esbuild-plugin-global-externals/-/esbuild-plugin-global-externals-2.1.2.tgz", "integrity": "sha1-wF7TWtgt+OasYWxouSwigr0IO6Q=", "license": "MIT"}, "node_modules/@fortawesome/fontawesome-common-types": {"version": "6.7.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@fortawesome/fontawesome-common-types/-/fontawesome-common-types-6.7.2.tgz", "integrity": "sha1-cSPXSwwecmeUrtEYR5XbzhIYZHA=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@fortawesome/fontawesome-svg-core": {"version": "6.7.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@fortawesome/fontawesome-svg-core/-/fontawesome-svg-core-6.7.2.tgz", "integrity": "sha1-CsYBNyTVzDJ8HrgTNbkTAKT84vI=", "license": "MIT", "dependencies": {"@fortawesome/fontawesome-common-types": "6.7.2"}, "engines": {"node": ">=6"}}, "node_modules/@fortawesome/free-brands-svg-icons": {"version": "6.7.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@fortawesome/free-brands-svg-icons/-/free-brands-svg-icons-6.7.2.tgz", "integrity": "sha1-Tr7oCY8x2lRG3age3DRAJetZsn4=", "license": "(CC-BY-4.0 AND MIT)", "dependencies": {"@fortawesome/fontawesome-common-types": "6.7.2"}, "engines": {"node": ">=6"}}, "node_modules/@fortawesome/free-regular-svg-icons": {"version": "6.7.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@fortawesome/free-regular-svg-icons/-/free-regular-svg-icons-6.7.2.tgz", "integrity": "sha1-8WUeVeZlGhVYmwVpUWII+cZflts=", "license": "(CC-BY-4.0 AND MIT)", "dependencies": {"@fortawesome/fontawesome-common-types": "6.7.2"}, "engines": {"node": ">=6"}}, "node_modules/@fortawesome/free-solid-svg-icons": {"version": "6.7.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@fortawesome/free-solid-svg-icons/-/free-solid-svg-icons-6.7.2.tgz", "integrity": "sha1-/iWIO164RkqCkYWZlQ0oPEZbV/Y=", "license": "(CC-BY-4.0 AND MIT)", "dependencies": {"@fortawesome/fontawesome-common-types": "6.7.2"}, "engines": {"node": ">=6"}}, "node_modules/@fortawesome/vue-fontawesome": {"version": "3.0.8", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@fortawesome/vue-fontawesome/-/vue-fontawesome-3.0.8.tgz", "integrity": "sha1-HoAy3xURc9gXSsn1oo2jwPWkleQ=", "license": "MIT", "peerDependencies": {"@fortawesome/fontawesome-svg-core": "~1 || ~6", "vue": ">= 3.0.0 < 4"}}, "node_modules/@hapi/hoek": {"version": "9.3.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@hapi/topo": {"version": "5.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0"}}, "node_modules/@humanfs/core": {"version": "0.19.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node": {"version": "0.16.6", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanfs/core": "^0.19.1", "@humanwhocodes/retry": "^0.3.0"}, "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node/node_modules/@humanwhocodes/retry": {"version": "0.3.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/retry": {"version": "0.4.3", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@intlify/bundle-utils": {"version": "5.5.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/bundle-utils/-/bundle-utils-5.5.0.tgz", "integrity": "sha1-rmnyzDGaoZ3SKl51h1PuciCN4G0=", "license": "MIT", "dependencies": {"@intlify/message-compiler": "9.3.0-beta.17", "@intlify/shared": "9.3.0-beta.17", "acorn": "^8.8.2", "escodegen": "^2.0.0", "estree-walker": "^2.0.2", "jsonc-eslint-parser": "^1.0.1", "magic-string": "^0.30.0", "source-map": "0.6.1", "yaml-eslint-parser": "^0.3.2"}, "engines": {"node": ">= 12"}, "peerDependenciesMeta": {"petite-vue-i18n": {"optional": true}, "vue-i18n": {"optional": true}}}, "node_modules/@intlify/core-base": {"version": "11.1.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/core-base/-/core-base-11.1.5.tgz", "integrity": "sha1-TqfH2J4SKDsgRvUfkKTpD8L94H0=", "license": "MIT", "dependencies": {"@intlify/message-compiler": "11.1.5", "@intlify/shared": "11.1.5"}, "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}}, "node_modules/@intlify/core-base/node_modules/@intlify/message-compiler": {"version": "11.1.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/message-compiler/-/message-compiler-11.1.5.tgz", "integrity": "sha1-POR5vpZO+lYV0R/gimFeBR9AYOw=", "license": "MIT", "dependencies": {"@intlify/shared": "11.1.5", "source-map-js": "^1.0.2"}, "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}}, "node_modules/@intlify/core-base/node_modules/@intlify/shared": {"version": "11.1.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/shared/-/shared-11.1.5.tgz", "integrity": "sha1-I26wsrAYdve1QN20B54gJ8/IPWM=", "license": "MIT", "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}}, "node_modules/@intlify/message-compiler": {"version": "9.3.0-beta.17", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/message-compiler/-/message-compiler-9.3.0-beta.17.tgz", "integrity": "sha1-vpyjpheSazu9irgN01Shu1eWnvE=", "license": "MIT", "dependencies": {"@intlify/shared": "9.3.0-beta.17", "source-map": "0.6.1"}, "engines": {"node": ">= 14"}, "funding": {"url": "https://github.com/sponsors/kazupon"}}, "node_modules/@intlify/shared": {"version": "9.3.0-beta.17", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/shared/-/shared-9.3.0-beta.17.tgz", "integrity": "sha1-EYDcsLMHQVVfrQti5GIYAugnLuU=", "license": "MIT", "engines": {"node": ">= 14"}, "funding": {"url": "https://github.com/sponsors/kazupon"}}, "node_modules/@intlify/unplugin-vue-i18n": {"version": "0.10.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/unplugin-vue-i18n/-/unplugin-vue-i18n-0.10.1.tgz", "integrity": "sha1-bh08hzl2r17ZOgbrmPNuTBuAIb4=", "license": "MIT", "dependencies": {"@intlify/bundle-utils": "^5.4.0", "@intlify/shared": "9.3.0-beta.17", "@rollup/pluginutils": "^5.0.2", "@vue/compiler-sfc": "^3.2.47", "debug": "^4.3.3", "fast-glob": "^3.2.12", "js-yaml": "^4.1.0", "json5": "^2.2.3", "pathe": "^1.0.0", "picocolors": "^1.0.0", "source-map": "0.6.1", "unplugin": "^1.1.0"}, "engines": {"node": ">= 14.16"}, "peerDependencies": {"petite-vue-i18n": "*", "vue-i18n": "*", "vue-i18n-bridge": "*"}, "peerDependenciesMeta": {"petite-vue-i18n": {"optional": true}, "vue-i18n": {"optional": true}, "vue-i18n-bridge": {"optional": true}}}, "node_modules/@intlify/unplugin-vue-i18n/node_modules/pathe": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pathe/-/pathe-1.1.2.tgz", "integrity": "sha1-bEy0epRWkuSKHd1uQJTRcFFkN+w=", "license": "MIT"}, "node_modules/@isaacs/cliui": {"version": "8.0.2", "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@isaacs/cliui/node_modules/ansi-regex": {"version": "6.1.0", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/emoji-regex": {"version": "9.2.2", "license": "MIT"}, "node_modules/@isaacs/cliui/node_modules/string-width": {"version": "5.1.2", "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@isaacs/cliui/node_modules/strip-ansi": {"version": "7.1.0", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/@isaacs/fs-minipass": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@isaacs/fs-minipass/-/fs-minipass-4.0.1.tgz", "integrity": "sha1-LVmuOrSzj7QnC/oj0w+OLobH/jI=", "dev": true, "license": "ISC", "dependencies": {"minipass": "^7.0.4"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.8", "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@one-ini/wasm": {"version": "0.1.1", "dev": true, "license": "MIT"}, "node_modules/@opentelemetry/api": {"version": "1.9.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@opentelemetry/api/-/api-1.9.0.tgz", "integrity": "sha1-0D66aCc9wPdQnio9XLoh6uEDef4=", "license": "Apache-2.0", "engines": {"node": ">=8.0.0"}}, "node_modules/@pkgjs/parseargs": {"version": "0.11.0", "license": "MIT", "optional": true, "engines": {"node": ">=14"}}, "node_modules/@pkgr/core": {"version": "0.2.7", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/pkgr"}}, "node_modules/@polka/url": {"version": "1.0.0-next.29", "dev": true, "license": "MIT"}, "node_modules/@rollup/pluginutils": {"version": "5.1.4", "license": "MIT", "dependencies": {"@types/estree": "^1.0.0", "estree-walker": "^2.0.2", "picomatch": "^4.0.2"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"rollup": "^1.20.0||^2.0.0||^3.0.0||^4.0.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}}}, "node_modules/@rollup/pluginutils/node_modules/picomatch": {"version": "4.0.2", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/@rollup/rollup-android-arm-eabi": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.41.1.tgz", "integrity": "sha1-858J9g1KVi3nJ8lg17ICos95dCQ=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-android-arm64": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.41.1.tgz", "integrity": "sha1-0Zr34jdgcX8dh51Mo9LNJHdC3/I=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-darwin-arm64": {"version": "4.41.1", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-darwin-x64": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.41.1.tgz", "integrity": "sha1-qmbSuhol5glQDhO+8G3A5xzAwNQ=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-freebsd-arm64": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.41.1.tgz", "integrity": "sha1-3xCntjFqDvECjGynGggRJMU34w0=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-freebsd-x64": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.41.1.tgz", "integrity": "sha1-o/3OigXpWwaMvLRuTfUYXkB9DDU=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-linux-arm-gnueabihf": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.41.1.tgz", "integrity": "sha1-SfdmxVODvQSYAUqddpJDSMLziQw=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm-musleabihf": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.41.1.tgz", "integrity": "sha1-HU19MvxVfhfVLhhXgXOB6jZeKVk=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-gnu": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.41.1.tgz", "integrity": "sha1-9PwxcmhEHpWJ7a076PYrbAMAm8E=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-musl": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.41.1.tgz", "integrity": "sha1-Y6HxsGccsXgi2rroJ/7w5EOuvrc=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-loongarch64-gnu": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.41.1.tgz", "integrity": "sha1-xlmwHMbAcwtUdXH8OXPh6VU2n5g=", "cpu": ["loong64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-powerpc64le-gnu": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.41.1.tgz", "integrity": "sha1-YS50b5rX5YSA+WTWXg1sP0quaag=", "cpu": ["ppc64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-gnu": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.41.1.tgz", "integrity": "sha1-RhDb0dz7uuMvvBDCCuc4essxEQw=", "cpu": ["riscv64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-musl": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.41.1.tgz", "integrity": "sha1-BUkR+rQNyD+vwh5HAZPAWBCPGdg=", "cpu": ["riscv64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-s390x-gnu": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.41.1.tgz", "integrity": "sha1-mIluyoASVHx/BL0H6qaJaCX54aU=", "cpu": ["s390x"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-gnu": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.41.1.tgz", "integrity": "sha1-Ac9WhEoeY27oDfs2TnLCtxQq2JY=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-musl": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.41.1.tgz", "integrity": "sha1-5nx1Md9t/wtMJBEB1AlmF/vKh8M=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-win32-arm64-msvc": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.41.1.tgz", "integrity": "sha1-furamEROWAZ03mmJKE5Lqs1I6mU=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-ia32-msvc": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.41.1.tgz", "integrity": "sha1-UWxLVPgFh7SjkKr0lAtAhwJx010=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-x64-msvc": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.41.1.tgz", "integrity": "sha1-hI+ZsNmTbZIiG7YHC67/TbaUejA=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@sec-ant/readable-stream": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/@sideway/address": {"version": "4.1.5", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0"}}, "node_modules/@sideway/formula": {"version": "3.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@sideway/pinpoint": {"version": "2.0.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@sindresorhus/merge-streams": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@storybook/builder-manager": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/builder-manager/-/builder-manager-7.6.20.tgz", "integrity": "sha1-1VCj8gkBLk44PmEyDqdWzd/bQW4=", "license": "MIT", "dependencies": {"@fal-works/esbuild-plugin-global-externals": "^2.1.2", "@storybook/core-common": "7.6.20", "@storybook/manager": "7.6.20", "@storybook/node-logger": "7.6.20", "@types/ejs": "^3.1.1", "@types/find-cache-dir": "^3.2.1", "@yarnpkg/esbuild-plugin-pnp": "^3.0.0-rc.10", "browser-assert": "^1.2.1", "ejs": "^3.1.8", "esbuild": "^0.18.0", "esbuild-plugin-alias": "^0.2.1", "express": "^4.17.3", "find-cache-dir": "^3.0.0", "fs-extra": "^11.1.0", "process": "^0.11.10", "util": "^0.12.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/builder-manager/node_modules/@esbuild/darwin-arm64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/darwin-arm64/-/darwin-arm64-0.18.20.tgz", "integrity": "sha1-CBcsvsz5X7w4M5mn85z73a6w18E=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "node_modules/@storybook/builder-manager/node_modules/esbuild": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/esbuild/-/esbuild-0.18.20.tgz", "integrity": "sha1-Rwn1o0gBtDt5mrfW2C9yhKm3p6Y=", "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"@esbuild/android-arm": "0.18.20", "@esbuild/android-arm64": "0.18.20", "@esbuild/android-x64": "0.18.20", "@esbuild/darwin-arm64": "0.18.20", "@esbuild/darwin-x64": "0.18.20", "@esbuild/freebsd-arm64": "0.18.20", "@esbuild/freebsd-x64": "0.18.20", "@esbuild/linux-arm": "0.18.20", "@esbuild/linux-arm64": "0.18.20", "@esbuild/linux-ia32": "0.18.20", "@esbuild/linux-loong64": "0.18.20", "@esbuild/linux-mips64el": "0.18.20", "@esbuild/linux-ppc64": "0.18.20", "@esbuild/linux-riscv64": "0.18.20", "@esbuild/linux-s390x": "0.18.20", "@esbuild/linux-x64": "0.18.20", "@esbuild/netbsd-x64": "0.18.20", "@esbuild/openbsd-x64": "0.18.20", "@esbuild/sunos-x64": "0.18.20", "@esbuild/win32-arm64": "0.18.20", "@esbuild/win32-ia32": "0.18.20", "@esbuild/win32-x64": "0.18.20"}}, "node_modules/@storybook/builder-manager/node_modules/fs-extra": {"version": "11.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fs-extra/-/fs-extra-11.3.0.tgz", "integrity": "sha1-DaztE2u69lpVWjJnGa+TGtx6MU0=", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/@storybook/channels": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/channels/-/channels-7.6.20.tgz", "integrity": "sha1-M9gpKxsW1/UEv3UcV6eSR30cOp4=", "license": "MIT", "dependencies": {"@storybook/client-logger": "7.6.20", "@storybook/core-events": "7.6.20", "@storybook/global": "^5.0.0", "qs": "^6.10.0", "telejson": "^7.2.0", "tiny-invariant": "^1.3.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/client-logger": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/client-logger/-/client-logger-7.6.20.tgz", "integrity": "sha1-HW6TRDCRzM1Q4mk3GqeGFy0MRlk=", "license": "MIT", "dependencies": {"@storybook/global": "^5.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/core-client": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/core-client/-/core-client-7.6.20.tgz", "integrity": "sha1-gxaB1kGU5NYEqFntPrRSmB9oJMU=", "license": "MIT", "dependencies": {"@storybook/client-logger": "7.6.20", "@storybook/preview-api": "7.6.20"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/core-common": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/core-common/-/core-common-7.6.20.tgz", "integrity": "sha1-Oio65XC9E9w0cmF4wOs2z2pk4qQ=", "license": "MIT", "dependencies": {"@storybook/core-events": "7.6.20", "@storybook/node-logger": "7.6.20", "@storybook/types": "7.6.20", "@types/find-cache-dir": "^3.2.1", "@types/node": "^18.0.0", "@types/node-fetch": "^2.6.4", "@types/pretty-hrtime": "^1.0.0", "chalk": "^4.1.0", "esbuild": "^0.18.0", "esbuild-register": "^3.5.0", "file-system-cache": "2.3.0", "find-cache-dir": "^3.0.0", "find-up": "^5.0.0", "fs-extra": "^11.1.0", "glob": "^10.0.0", "handlebars": "^4.7.7", "lazy-universal-dotenv": "^4.0.0", "node-fetch": "^2.0.0", "picomatch": "^2.3.0", "pkg-dir": "^5.0.0", "pretty-hrtime": "^1.0.3", "resolve-from": "^5.0.0", "ts-dedent": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/core-common/node_modules/@esbuild/darwin-arm64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/darwin-arm64/-/darwin-arm64-0.18.20.tgz", "integrity": "sha1-CBcsvsz5X7w4M5mn85z73a6w18E=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "node_modules/@storybook/core-common/node_modules/@types/node": {"version": "18.19.110", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/node/-/node-18.19.110.tgz", "integrity": "sha1-M+JfoXlrpQI87hN/JPFdMy0tRdE=", "license": "MIT", "dependencies": {"undici-types": "~5.26.4"}}, "node_modules/@storybook/core-common/node_modules/esbuild": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/esbuild/-/esbuild-0.18.20.tgz", "integrity": "sha1-Rwn1o0gBtDt5mrfW2C9yhKm3p6Y=", "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"@esbuild/android-arm": "0.18.20", "@esbuild/android-arm64": "0.18.20", "@esbuild/android-x64": "0.18.20", "@esbuild/darwin-arm64": "0.18.20", "@esbuild/darwin-x64": "0.18.20", "@esbuild/freebsd-arm64": "0.18.20", "@esbuild/freebsd-x64": "0.18.20", "@esbuild/linux-arm": "0.18.20", "@esbuild/linux-arm64": "0.18.20", "@esbuild/linux-ia32": "0.18.20", "@esbuild/linux-loong64": "0.18.20", "@esbuild/linux-mips64el": "0.18.20", "@esbuild/linux-ppc64": "0.18.20", "@esbuild/linux-riscv64": "0.18.20", "@esbuild/linux-s390x": "0.18.20", "@esbuild/linux-x64": "0.18.20", "@esbuild/netbsd-x64": "0.18.20", "@esbuild/openbsd-x64": "0.18.20", "@esbuild/sunos-x64": "0.18.20", "@esbuild/win32-arm64": "0.18.20", "@esbuild/win32-ia32": "0.18.20", "@esbuild/win32-x64": "0.18.20"}}, "node_modules/@storybook/core-common/node_modules/fs-extra": {"version": "11.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fs-extra/-/fs-extra-11.3.0.tgz", "integrity": "sha1-DaztE2u69lpVWjJnGa+TGtx6MU0=", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/@storybook/core-common/node_modules/resolve-from": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/resolve-from/-/resolve-from-5.0.0.tgz", "integrity": "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@storybook/core-common/node_modules/undici-types": {"version": "5.26.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/undici-types/-/undici-types-5.26.5.tgz", "integrity": "sha1-vNU5iT0AtW6WT9JlekhmsiGmVhc=", "license": "MIT"}, "node_modules/@storybook/core-events": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/core-events/-/core-events-7.6.20.tgz", "integrity": "sha1-ZkjWYdHJaEGkwqcQo1dZsBtqBqE=", "license": "MIT", "dependencies": {"ts-dedent": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/core-server": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/core-server/-/core-server-7.6.20.tgz", "integrity": "sha1-+hQ/vK1k+3sPDcbVVdCDxQakSrQ=", "license": "MIT", "dependencies": {"@aw-web-design/x-default-browser": "1.4.126", "@discoveryjs/json-ext": "^0.5.3", "@storybook/builder-manager": "7.6.20", "@storybook/channels": "7.6.20", "@storybook/core-common": "7.6.20", "@storybook/core-events": "7.6.20", "@storybook/csf": "^0.1.2", "@storybook/csf-tools": "7.6.20", "@storybook/docs-mdx": "^0.1.0", "@storybook/global": "^5.0.0", "@storybook/manager": "7.6.20", "@storybook/node-logger": "7.6.20", "@storybook/preview-api": "7.6.20", "@storybook/telemetry": "7.6.20", "@storybook/types": "7.6.20", "@types/detect-port": "^1.3.0", "@types/node": "^18.0.0", "@types/pretty-hrtime": "^1.0.0", "@types/semver": "^7.3.4", "better-opn": "^3.0.2", "chalk": "^4.1.0", "cli-table3": "^0.6.1", "compression": "^1.7.4", "detect-port": "^1.3.0", "express": "^4.17.3", "fs-extra": "^11.1.0", "globby": "^11.0.2", "lodash": "^4.17.21", "open": "^8.4.0", "pretty-hrtime": "^1.0.3", "prompts": "^2.4.0", "read-pkg-up": "^7.0.1", "semver": "^7.3.7", "telejson": "^7.2.0", "tiny-invariant": "^1.3.1", "ts-dedent": "^2.0.0", "util": "^0.12.4", "util-deprecate": "^1.0.2", "watchpack": "^2.2.0", "ws": "^8.2.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/core-server/node_modules/@types/node": {"version": "18.19.110", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/node/-/node-18.19.110.tgz", "integrity": "sha1-M+JfoXlrpQI87hN/JPFdMy0tRdE=", "license": "MIT", "dependencies": {"undici-types": "~5.26.4"}}, "node_modules/@storybook/core-server/node_modules/define-lazy-prop": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz", "integrity": "sha1-P3rkIRKbyqrJvHSQXJigAJ7J7n8=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@storybook/core-server/node_modules/fs-extra": {"version": "11.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fs-extra/-/fs-extra-11.3.0.tgz", "integrity": "sha1-DaztE2u69lpVWjJnGa+TGtx6MU0=", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/@storybook/core-server/node_modules/is-docker": {"version": "2.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-docker/-/is-docker-2.2.1.tgz", "integrity": "sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=", "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@storybook/core-server/node_modules/is-wsl": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-wsl/-/is-wsl-2.2.0.tgz", "integrity": "sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=", "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@storybook/core-server/node_modules/open": {"version": "8.4.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/open/-/open-8.4.2.tgz", "integrity": "sha1-W1/+Ko95Pc0qrXPlUMuHtZywhPk=", "license": "MIT", "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@storybook/core-server/node_modules/undici-types": {"version": "5.26.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/undici-types/-/undici-types-5.26.5.tgz", "integrity": "sha1-vNU5iT0AtW6WT9JlekhmsiGmVhc=", "license": "MIT"}, "node_modules/@storybook/csf": {"version": "0.1.13", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/csf/-/csf-0.1.13.tgz", "integrity": "sha1-yKm+oq5Rij2XAFRnSPowqLB/f4A=", "license": "MIT", "dependencies": {"type-fest": "^2.19.0"}}, "node_modules/@storybook/csf-plugin": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/csf-plugin/-/csf-plugin-7.6.20.tgz", "integrity": "sha1-DnnljV7UfftHKx3CArDnVMIewzs=", "license": "MIT", "dependencies": {"@storybook/csf-tools": "7.6.20", "unplugin": "^1.3.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/csf-tools": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/csf-tools/-/csf-tools-7.6.20.tgz", "integrity": "sha1-/dn6lFlyCmJ+g+MdODlyHbxlXyI=", "license": "MIT", "dependencies": {"@babel/generator": "^7.23.0", "@babel/parser": "^7.23.0", "@babel/traverse": "^7.23.2", "@babel/types": "^7.23.0", "@storybook/csf": "^0.1.2", "@storybook/types": "7.6.20", "fs-extra": "^11.1.0", "recast": "^0.23.1", "ts-dedent": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/csf-tools/node_modules/fs-extra": {"version": "11.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fs-extra/-/fs-extra-11.3.0.tgz", "integrity": "sha1-DaztE2u69lpVWjJnGa+TGtx6MU0=", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/@storybook/csf/node_modules/type-fest": {"version": "2.19.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/type-fest/-/type-fest-2.19.0.tgz", "integrity": "sha1-iAaAFbszA2pZi5UuVekxGmD9Ops=", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@storybook/docs-mdx": {"version": "0.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/docs-mdx/-/docs-mdx-0.1.0.tgz", "integrity": "sha1-M7oOOdFGHK8Ei1fbNUssxBBwUxY=", "license": "MIT"}, "node_modules/@storybook/docs-tools": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/docs-tools/-/docs-tools-7.6.20.tgz", "integrity": "sha1-Km3UAsiA4k7GvshBG+7onP5p+TI=", "license": "MIT", "dependencies": {"@storybook/core-common": "7.6.20", "@storybook/preview-api": "7.6.20", "@storybook/types": "7.6.20", "@types/doctrine": "^0.0.3", "assert": "^2.1.0", "doctrine": "^3.0.0", "lodash": "^4.17.21"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/global": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/global/-/global-5.0.0.tgz", "integrity": "sha1-t5PTS5T1csHX2eD0T6xODbyVcu0=", "license": "MIT"}, "node_modules/@storybook/manager": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/manager/-/manager-7.6.20.tgz", "integrity": "sha1-62Gf6NM0RuWBp7HDBQZEwZY2TTk=", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/node-logger": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/node-logger/-/node-logger-7.6.20.tgz", "integrity": "sha1-wMqQz2jPMdhM3PU8ds7CJ2lAfs4=", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/preview": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/preview/-/preview-7.6.20.tgz", "integrity": "sha1-3zlznc5uGD768GqMFalFnwGeYxs=", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/preview-api": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/preview-api/-/preview-api-7.6.20.tgz", "integrity": "sha1-aIpDXuLP5X7rHjBTwYAlqeCgO7s=", "license": "MIT", "dependencies": {"@storybook/channels": "7.6.20", "@storybook/client-logger": "7.6.20", "@storybook/core-events": "7.6.20", "@storybook/csf": "^0.1.2", "@storybook/global": "^5.0.0", "@storybook/types": "7.6.20", "@types/qs": "^6.9.5", "dequal": "^2.0.2", "lodash": "^4.17.21", "memoizerific": "^1.11.3", "qs": "^6.10.0", "synchronous-promise": "^2.0.15", "ts-dedent": "^2.0.0", "util-deprecate": "^1.0.2"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/telemetry": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/telemetry/-/telemetry-7.6.20.tgz", "integrity": "sha1-WzcF61EAshBw12dn3eEEDtXZs1s=", "license": "MIT", "dependencies": {"@storybook/client-logger": "7.6.20", "@storybook/core-common": "7.6.20", "@storybook/csf-tools": "7.6.20", "chalk": "^4.1.0", "detect-package-manager": "^2.0.1", "fetch-retry": "^5.0.2", "fs-extra": "^11.1.0", "read-pkg-up": "^7.0.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/telemetry/node_modules/fs-extra": {"version": "11.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fs-extra/-/fs-extra-11.3.0.tgz", "integrity": "sha1-DaztE2u69lpVWjJnGa+TGtx6MU0=", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/@storybook/types": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/types/-/types-7.6.20.tgz", "integrity": "sha1-uNYrMJFLNeZ1Cx9JN9pTJDLwKJA=", "license": "MIT", "dependencies": {"@storybook/channels": "7.6.20", "@types/babel__core": "^7.0.0", "@types/express": "^4.7.0", "file-system-cache": "2.3.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/vue3": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/vue3/-/vue3-7.6.20.tgz", "integrity": "sha1-1UWpPIfyTEdFOhHzvWVVCB3IX54=", "license": "MIT", "dependencies": {"@storybook/core-client": "7.6.20", "@storybook/docs-tools": "7.6.20", "@storybook/global": "^5.0.0", "@storybook/preview-api": "7.6.20", "@storybook/types": "7.6.20", "@vue/compiler-core": "^3.0.0", "lodash": "^4.17.21", "ts-dedent": "^2.0.0", "type-fest": "~2.19", "vue-component-type-helpers": "latest"}, "engines": {"node": ">=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "peerDependencies": {"vue": "^3.0.0"}}, "node_modules/@storybook/vue3/node_modules/type-fest": {"version": "2.19.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/type-fest/-/type-fest-2.19.0.tgz", "integrity": "sha1-iAaAFbszA2pZi5UuVekxGmD9Ops=", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@tailwindcss/container-queries": {"version": "0.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tailwindcss/container-queries/-/container-queries-0.1.1.tgz", "integrity": "sha1-mnWc4suHNqTGoMuTrrdAVzpzGXQ=", "license": "MIT", "peerDependencies": {"tailwindcss": ">=3.2.0"}}, "node_modules/@tailwindcss/node": {"version": "4.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tailwindcss/node/-/node-4.1.10.tgz", "integrity": "sha1-elOiJM3XmpJu2ZC7+Xx03p2t9ZU=", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.3.0", "enhanced-resolve": "^5.18.1", "jiti": "^2.4.2", "lightningcss": "1.30.1", "magic-string": "^0.30.17", "source-map-js": "^1.2.1", "tailwindcss": "4.1.10"}}, "node_modules/@tailwindcss/oxide": {"version": "4.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tailwindcss/oxide/-/oxide-4.1.10.tgz", "integrity": "sha1-uK1q5ni1S7UzwgdAkqreusCm2P4=", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"detect-libc": "^2.0.4", "tar": "^7.4.3"}, "engines": {"node": ">= 10"}, "optionalDependencies": {"@tailwindcss/oxide-android-arm64": "4.1.10", "@tailwindcss/oxide-darwin-arm64": "4.1.10", "@tailwindcss/oxide-darwin-x64": "4.1.10", "@tailwindcss/oxide-freebsd-x64": "4.1.10", "@tailwindcss/oxide-linux-arm-gnueabihf": "4.1.10", "@tailwindcss/oxide-linux-arm64-gnu": "4.1.10", "@tailwindcss/oxide-linux-arm64-musl": "4.1.10", "@tailwindcss/oxide-linux-x64-gnu": "4.1.10", "@tailwindcss/oxide-linux-x64-musl": "4.1.10", "@tailwindcss/oxide-wasm32-wasi": "4.1.10", "@tailwindcss/oxide-win32-arm64-msvc": "4.1.10", "@tailwindcss/oxide-win32-x64-msvc": "4.1.10"}}, "node_modules/@tailwindcss/oxide-android-arm64": {"version": "4.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tailwindcss/oxide-android-arm64/-/oxide-android-arm64-4.1.10.tgz", "integrity": "sha1-rQ88v6IZ4e5fyK1xcIhf7aOXxOM=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-darwin-arm64": {"version": "4.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tailwindcss/oxide-darwin-arm64/-/oxide-darwin-arm64-4.1.10.tgz", "integrity": "sha1-2NdE+TMQtFzhZCCprd0cQymEiSk=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-darwin-x64": {"version": "4.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tailwindcss/oxide-darwin-x64/-/oxide-darwin-x64-4.1.10.tgz", "integrity": "sha1-R2SQ0flVkqCYAaU7SEZuUGXXVT8=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-freebsd-x64": {"version": "4.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tailwindcss/oxide-freebsd-x64/-/oxide-freebsd-x64-4.1.10.tgz", "integrity": "sha1-e3zLgTWSIJIW7TkYfrhRDOa0/J0=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-linux-arm-gnueabihf": {"version": "4.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tailwindcss/oxide-linux-arm-gnueabihf/-/oxide-linux-arm-gnueabihf-4.1.10.tgz", "integrity": "sha1-nyI8eZTahGufPHCsC1cTNxybOzI=", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-linux-arm64-gnu": {"version": "4.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tailwindcss/oxide-linux-arm64-gnu/-/oxide-linux-arm64-gnu-4.1.10.tgz", "integrity": "sha1-WEEuajWagxRLMLQV9jelLIIH8xE=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-linux-arm64-musl": {"version": "4.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tailwindcss/oxide-linux-arm64-musl/-/oxide-linux-arm64-musl-4.1.10.tgz", "integrity": "sha1-PthouAGifozTWmFYVbyU/SeGpug=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-linux-x64-gnu": {"version": "4.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tailwindcss/oxide-linux-x64-gnu/-/oxide-linux-x64-gnu-4.1.10.tgz", "integrity": "sha1-rKFcxM+dzWh+2g9c0rwfS/tIVWI=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-linux-x64-musl": {"version": "4.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tailwindcss/oxide-linux-x64-musl/-/oxide-linux-x64-musl-4.1.10.tgz", "integrity": "sha1-DHfR6U5Jmp+FyAAT5gUt2Y08/uQ=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-wasm32-wasi": {"version": "4.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.10.tgz", "integrity": "sha1-bnSUJNtPbgdjcaZtp8Ta8fzU+d8=", "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "cpu": ["wasm32"], "dev": true, "license": "MIT", "optional": true, "dependencies": {"@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10", "@tybys/wasm-util": "^0.9.0", "tslib": "^2.8.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@tailwindcss/oxide-win32-arm64-msvc": {"version": "4.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.1.10.tgz", "integrity": "sha1-4WY7WpVCXw9Fj2Fjme2fZwfUp4Y=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/oxide-win32-x64-msvc": {"version": "4.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tailwindcss/oxide-win32-x64-msvc/-/oxide-win32-x64-msvc-4.1.10.tgz", "integrity": "sha1-3j1OizjDHK8lIq0Mbw7961A0/JU=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@tailwindcss/vite": {"version": "4.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tailwindcss/vite/-/vite-4.1.10.tgz", "integrity": "sha1-n/o5aj+F0x9T7qpLrDPrAoa8lV0=", "dev": true, "license": "MIT", "dependencies": {"@tailwindcss/node": "4.1.10", "@tailwindcss/oxide": "4.1.10", "tailwindcss": "4.1.10"}, "peerDependencies": {"vite": "^5.2.0 || ^6"}}, "node_modules/@tsconfig/node22": {"version": "22.0.2", "dev": true, "license": "MIT"}, "node_modules/@types/babel__core": {"version": "7.20.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/babel__core/-/babel__core-7.20.5.tgz", "integrity": "sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=", "license": "MIT", "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.27.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/babel__generator/-/babel__generator-7.27.0.tgz", "integrity": "sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=", "license": "MIT", "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/babel__template/-/babel__template-7.4.4.tgz", "integrity": "sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=", "license": "MIT", "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.20.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "integrity": "sha1-lozcI2bsPaFZ9hFmQo7kDzcOVsI=", "license": "MIT", "dependencies": {"@babel/types": "^7.20.7"}}, "node_modules/@types/body-parser": {"version": "1.19.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/body-parser/-/body-parser-1.19.5.tgz", "integrity": "sha1-BM6aO2d9yL1oGhfaGrmDXcnT7eQ=", "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/chai": {"version": "5.2.2", "dev": true, "license": "MIT", "dependencies": {"@types/deep-eql": "*"}}, "node_modules/@types/connect": {"version": "3.4.38", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/connect/-/connect-3.4.38.tgz", "integrity": "sha1-W6fzvE+73q/43e2VLl/yzFP42Fg=", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/deep-eql": {"version": "4.0.2", "dev": true, "license": "MIT"}, "node_modules/@types/detect-port": {"version": "1.3.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/detect-port/-/detect-port-1.3.5.tgz", "integrity": "sha1-3uzeFDJFmJ3uDoIRXzyrpe4Op0c=", "license": "MIT"}, "node_modules/@types/doctrine": {"version": "0.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/doctrine/-/doctrine-0.0.3.tgz", "integrity": "sha1-6JLSk8ksnB0/mvcsFaVU+8fgiVo=", "license": "MIT"}, "node_modules/@types/ejs": {"version": "3.1.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/ejs/-/ejs-3.1.5.tgz", "integrity": "sha1-Sdc4JXzHO6/kXBPLj/JAaDtNURc=", "license": "MIT"}, "node_modules/@types/estree": {"version": "1.0.7", "license": "MIT"}, "node_modules/@types/express": {"version": "4.17.22", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/express/-/express-4.17.22.tgz", "integrity": "sha1-FM/PEg9+tW67jKd7f6mhTSHefJY=", "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "4.19.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/express-serve-static-core/-/express-serve-static-core-4.19.6.tgz", "integrity": "sha1-4BMkwqAk/zZ9ksZvSFU87Qq1Amc=", "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/find-cache-dir": {"version": "3.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/find-cache-dir/-/find-cache-dir-3.2.1.tgz", "integrity": "sha1-e5WaS5ZDoeahpf5JAyaTzDZ3NQE=", "license": "MIT"}, "node_modules/@types/http-errors": {"version": "2.0.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/http-errors/-/http-errors-2.0.4.tgz", "integrity": "sha1-frR3JsORtzRabsNa1/TeRpz1uk8=", "license": "MIT"}, "node_modules/@types/jsdom": {"version": "21.1.7", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/tough-cookie": "*", "parse5": "^7.0.0"}}, "node_modules/@types/json-schema": {"version": "7.0.15", "dev": true, "license": "MIT"}, "node_modules/@types/mime": {"version": "1.3.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/mime/-/mime-1.3.5.tgz", "integrity": "sha1-HvMC4Bz30rWg+lJnkMkSO/HQZpA=", "license": "MIT"}, "node_modules/@types/node": {"version": "22.15.29", "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/node-fetch": {"version": "2.6.12", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/node-fetch/-/node-fetch-2.6.12.tgz", "integrity": "sha1-irXD74Mw8TEAp0eeLNVtM4aDCgM=", "license": "MIT", "dependencies": {"@types/node": "*", "form-data": "^4.0.0"}}, "node_modules/@types/normalize-package-data": {"version": "2.4.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/normalize-package-data/-/normalize-package-data-2.4.4.tgz", "integrity": "sha1-VuLMJsOXwDj6sOOpF6EtXFkJ6QE=", "license": "MIT"}, "node_modules/@types/pretty-hrtime": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/pretty-hrtime/-/pretty-hrtime-1.0.3.tgz", "integrity": "sha1-7hvYyfegGzRFeGqtDvI6ul9RGkQ=", "license": "MIT"}, "node_modules/@types/qs": {"version": "6.14.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/qs/-/qs-6.14.0.tgz", "integrity": "sha1-2LYM7PYvLbD7aOXgBgd7kXi4XeU=", "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/range-parser/-/range-parser-1.2.7.tgz", "integrity": "sha1-UK5DU+qt3AQEQnmBL1LIxlhX28s=", "license": "MIT"}, "node_modules/@types/semver": {"version": "7.7.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/semver/-/semver-7.7.0.tgz", "integrity": "sha1-ZMRBva4DOzeLbu99DD13wym5N44=", "license": "MIT"}, "node_modules/@types/send": {"version": "0.17.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/send/-/send-0.17.4.tgz", "integrity": "sha1-ZhnNJOcnB5NwLk5qS5WKkBDPxXo=", "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/serve-static": {"version": "1.15.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/serve-static/-/serve-static-1.15.7.tgz", "integrity": "sha1-IhdLvXT7l/4wMQlzjptcLzBk9xQ=", "license": "MIT", "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "node_modules/@types/sinonjs__fake-timers": {"version": "8.1.1", "dev": true, "license": "MIT"}, "node_modules/@types/sizzle": {"version": "2.3.9", "dev": true, "license": "MIT"}, "node_modules/@types/tough-cookie": {"version": "4.0.5", "dev": true, "license": "MIT"}, "node_modules/@types/tunnel": {"version": "0.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/tunnel/-/tunnel-0.0.3.tgz", "integrity": "sha1-8QnnMLBysxNjR1YfxVjJNYu4xuk=", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/yauzl": {"version": "2.10.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/node": "*"}}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "8.33.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.10.0", "@typescript-eslint/scope-manager": "8.33.0", "@typescript-eslint/type-utils": "8.33.0", "@typescript-eslint/utils": "8.33.0", "@typescript-eslint/visitor-keys": "8.33.0", "graphemer": "^1.4.0", "ignore": "^7.0.0", "natural-compare": "^1.4.0", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^8.33.0", "eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/eslint-plugin/node_modules/ignore": {"version": "7.0.5", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/@typescript-eslint/parser": {"version": "8.33.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/scope-manager": "8.33.0", "@typescript-eslint/types": "8.33.0", "@typescript-eslint/typescript-estree": "8.33.0", "@typescript-eslint/visitor-keys": "8.33.0", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/project-service": {"version": "8.33.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.33.0", "@typescript-eslint/types": "^8.33.0", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/scope-manager": {"version": "8.33.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "8.33.0", "@typescript-eslint/visitor-keys": "8.33.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/tsconfig-utils": {"version": "8.33.0", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/type-utils": {"version": "8.33.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "8.33.0", "@typescript-eslint/utils": "8.33.0", "debug": "^4.3.4", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/types": {"version": "8.33.0", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "8.33.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/project-service": "8.33.0", "@typescript-eslint/tsconfig-utils": "8.33.0", "@typescript-eslint/types": "8.33.0", "@typescript-eslint/visitor-keys": "8.33.0", "debug": "^4.3.4", "fast-glob": "^3.3.2", "is-glob": "^4.0.3", "minimatch": "^9.0.4", "semver": "^7.6.0", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/utils": {"version": "8.33.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.7.0", "@typescript-eslint/scope-manager": "8.33.0", "@typescript-eslint/types": "8.33.0", "@typescript-eslint/typescript-estree": "8.33.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "8.33.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "8.33.0", "eslint-visitor-keys": "^4.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/visitor-keys/node_modules/eslint-visitor-keys": {"version": "4.2.0", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@typespec/ts-http-runtime": {"version": "0.2.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@typespec/ts-http-runtime/-/ts-http-runtime-0.2.2.tgz", "integrity": "sha1-oMdFjtmarm1+si78F6g5zsC0obM=", "license": "MIT", "dependencies": {"http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.0", "tslib": "^2.6.2"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@vitejs/plugin-vue": {"version": "5.2.4", "dev": true, "license": "MIT", "engines": {"node": "^18.0.0 || >=20.0.0"}, "peerDependencies": {"vite": "^5.0.0 || ^6.0.0", "vue": "^3.2.25"}}, "node_modules/@vitest/eslint-plugin": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/utils": "^8.24.0"}, "peerDependencies": {"eslint": ">= 8.57.0", "typescript": ">= 5.0.0", "vitest": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}, "vitest": {"optional": true}}}, "node_modules/@vitest/expect": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "^5.2.2", "@vitest/spy": "3.2.0", "@vitest/utils": "3.2.0", "chai": "^5.2.0", "tinyrainbow": "^2.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/mocker": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"@vitest/spy": "3.2.0", "estree-walker": "^3.0.3", "magic-string": "^0.30.17"}, "funding": {"url": "https://opencollective.com/vitest"}, "peerDependencies": {"msw": "^2.4.9", "vite": "^5.0.0 || ^6.0.0 || ^7.0.0-0"}, "peerDependenciesMeta": {"msw": {"optional": true}, "vite": {"optional": true}}}, "node_modules/@vitest/mocker/node_modules/estree-walker": {"version": "3.0.3", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "^1.0.0"}}, "node_modules/@vitest/pretty-format": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"tinyrainbow": "^2.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/runner": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"@vitest/utils": "3.2.0", "pathe": "^2.0.3"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/snapshot": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"@vitest/pretty-format": "3.2.0", "magic-string": "^0.30.17", "pathe": "^2.0.3"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/spy": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"tinyspy": "^4.0.3"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/utils": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"@vitest/pretty-format": "3.2.0", "loupe": "^3.1.3", "tinyrainbow": "^2.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@volar/language-core": {"version": "2.4.14", "dev": true, "license": "MIT", "dependencies": {"@volar/source-map": "2.4.14"}}, "node_modules/@volar/source-map": {"version": "2.4.14", "dev": true, "license": "MIT"}, "node_modules/@volar/typescript": {"version": "2.4.14", "dev": true, "license": "MIT", "dependencies": {"@volar/language-core": "2.4.14", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8"}}, "node_modules/@vue/babel-helper-vue-transform-on": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/@vue/babel-plugin-jsx": {"version": "1.4.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.25.9", "@babel/helper-plugin-utils": "^7.26.5", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/template": "^7.26.9", "@babel/traverse": "^7.26.9", "@babel/types": "^7.26.9", "@vue/babel-helper-vue-transform-on": "1.4.0", "@vue/babel-plugin-resolve-type": "1.4.0", "@vue/shared": "^3.5.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}}}, "node_modules/@vue/babel-plugin-resolve-type": {"version": "1.4.0", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.26.2", "@babel/helper-module-imports": "^7.25.9", "@babel/helper-plugin-utils": "^7.26.5", "@babel/parser": "^7.26.9", "@vue/compiler-sfc": "^3.5.13"}, "funding": {"url": "https://github.com/sponsors/sxzz"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@vue/compiler-core": {"version": "3.5.18", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/compiler-core/-/compiler-core-3.5.18.tgz", "integrity": "sha1-UhoTjN2XDZv9J+QhaNEvd6BLIHQ=", "license": "MIT", "dependencies": {"@babel/parser": "^7.28.0", "@vue/shared": "3.5.18", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.1"}}, "node_modules/@vue/compiler-core/node_modules/entities": {"version": "4.5.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/@vue/compiler-dom": {"version": "3.5.18", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/compiler-dom/-/compiler-dom-3.5.18.tgz", "integrity": "sha1-4TUESSwwYexbvmoueJ8VJh1PA6c=", "license": "MIT", "dependencies": {"@vue/compiler-core": "3.5.18", "@vue/shared": "3.5.18"}}, "node_modules/@vue/compiler-sfc": {"version": "3.5.18", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/compiler-sfc/-/compiler-sfc-3.5.18.tgz", "integrity": "sha1-uh6ElWEzfYCZN5lM2vkAU5VC7so=", "license": "MIT", "dependencies": {"@babel/parser": "^7.28.0", "@vue/compiler-core": "3.5.18", "@vue/compiler-dom": "3.5.18", "@vue/compiler-ssr": "3.5.18", "@vue/shared": "3.5.18", "estree-walker": "^2.0.2", "magic-string": "^0.30.17", "postcss": "^8.5.6", "source-map-js": "^1.2.1"}}, "node_modules/@vue/compiler-ssr": {"version": "3.5.18", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/compiler-ssr/-/compiler-ssr-3.5.18.tgz", "integrity": "sha1-rs3gsL/yaKnJAUumZ5kwfEp4Qyg=", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.18", "@vue/shared": "3.5.18"}}, "node_modules/@vue/compiler-vue2": {"version": "2.7.16", "dev": true, "license": "MIT", "dependencies": {"de-indent": "^1.0.2", "he": "^1.2.0"}}, "node_modules/@vue/devtools-api": {"version": "7.7.6", "license": "MIT", "dependencies": {"@vue/devtools-kit": "^7.7.6"}}, "node_modules/@vue/devtools-core": {"version": "7.7.6", "dev": true, "license": "MIT", "dependencies": {"@vue/devtools-kit": "^7.7.6", "@vue/devtools-shared": "^7.7.6", "mitt": "^3.0.1", "nanoid": "^5.1.0", "pathe": "^2.0.3", "vite-hot-client": "^2.0.4"}, "peerDependencies": {"vue": "^3.0.0"}}, "node_modules/@vue/devtools-core/node_modules/nanoid": {"version": "5.1.5", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.js"}, "engines": {"node": "^18 || >=20"}}, "node_modules/@vue/devtools-kit": {"version": "7.7.6", "license": "MIT", "dependencies": {"@vue/devtools-shared": "^7.7.6", "birpc": "^2.3.0", "hookable": "^5.5.3", "mitt": "^3.0.1", "perfect-debounce": "^1.0.0", "speakingurl": "^14.0.1", "superjson": "^2.2.2"}}, "node_modules/@vue/devtools-shared": {"version": "7.7.6", "license": "MIT", "dependencies": {"rfdc": "^1.4.1"}}, "node_modules/@vue/eslint-config-prettier": {"version": "10.2.0", "dev": true, "license": "MIT", "dependencies": {"eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2"}, "peerDependencies": {"eslint": ">= 8.21.0", "prettier": ">= 3.0.0"}}, "node_modules/@vue/eslint-config-typescript": {"version": "14.5.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/utils": "^8.26.0", "fast-glob": "^3.3.3", "typescript-eslint": "^8.26.0", "vue-eslint-parser": "^10.1.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependencies": {"eslint": "^9.10.0", "eslint-plugin-vue": "^9.28.0 || ^10.0.0", "typescript": ">=4.8.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@vue/language-core": {"version": "2.2.10", "dev": true, "license": "MIT", "dependencies": {"@volar/language-core": "~2.4.11", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^1.0.3", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@vue/reactivity": {"version": "3.5.18", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/reactivity/-/reactivity-3.5.18.tgz", "integrity": "sha1-/jIWbjk4gyxUtBNOYOm1jKfZvbQ=", "license": "MIT", "dependencies": {"@vue/shared": "3.5.18"}}, "node_modules/@vue/runtime-core": {"version": "3.5.18", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/runtime-core/-/runtime-core-3.5.18.tgz", "integrity": "sha1-nprouUkVSLU9DOor8ldG0nxS4ZE=", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.18", "@vue/shared": "3.5.18"}}, "node_modules/@vue/runtime-dom": {"version": "3.5.18", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/runtime-dom/-/runtime-dom-3.5.18.tgz", "integrity": "sha1-EVCVLRBItYIuTx3YrtJGZcuyIQc=", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.18", "@vue/runtime-core": "3.5.18", "@vue/shared": "3.5.18", "csstype": "^3.1.3"}}, "node_modules/@vue/server-renderer": {"version": "3.5.18", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/server-renderer/-/server-renderer-3.5.18.tgz", "integrity": "sha1-6fome5WzodjN3KdiN35d4q6RIr0=", "license": "MIT", "dependencies": {"@vue/compiler-ssr": "3.5.18", "@vue/shared": "3.5.18"}, "peerDependencies": {"vue": "3.5.18"}}, "node_modules/@vue/shared": {"version": "3.5.18", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/shared/-/shared-3.5.18.tgz", "integrity": "sha1-Up8kqI0+1njVD9XAdFWEH76KyV4=", "license": "MIT"}, "node_modules/@vue/test-utils": {"version": "2.4.6", "dev": true, "license": "MIT", "dependencies": {"js-beautify": "^1.14.9", "vue-component-type-helpers": "^2.0.0"}}, "node_modules/@vue/tsconfig": {"version": "0.7.0", "dev": true, "license": "MIT", "peerDependencies": {"typescript": "5.x", "vue": "^3.4.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}, "vue": {"optional": true}}}, "node_modules/@vuelidate/core": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vuelidate/core/-/core-2.0.3.tgz", "integrity": "sha1-QEaMXtFbcr3ogKAmsGmcLw8ezt4=", "license": "MIT", "dependencies": {"vue-demi": "^0.13.11"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^2.0.0 || >=3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@vuelidate/core/node_modules/vue-demi": {"version": "0.13.11", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-demi/-/vue-demi-0.13.11.tgz", "integrity": "sha1-fZA2m9rol02HsZc1ZK05AYJBDZk=", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@vuelidate/validators": {"version": "2.0.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vuelidate/validators/-/validators-2.0.4.tgz", "integrity": "sha1-CoinsrGPFf2cOECVWT82mm9zhOk=", "license": "MIT", "dependencies": {"vue-demi": "^0.13.11"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^2.0.0 || >=3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@vuelidate/validators/node_modules/vue-demi": {"version": "0.13.11", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-demi/-/vue-demi-0.13.11.tgz", "integrity": "sha1-fZA2m9rol02HsZc1ZK05AYJBDZk=", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@yarnpkg/esbuild-plugin-pnp": {"version": "3.0.0-rc.15", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@yarnpkg/esbuild-plugin-pnp/-/esbuild-plugin-pnp-3.0.0-rc.15.tgz", "integrity": "sha1-TkDn0usoglyaNaudBMNjkx18Dmc=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"tslib": "^2.4.0"}, "engines": {"node": ">=14.15.0"}, "peerDependencies": {"esbuild": ">=0.10.0"}}, "node_modules/abbrev": {"version": "2.0.0", "dev": true, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/accepts": {"version": "1.3.8", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/accepts/-/accepts-1.3.8.tgz", "integrity": "sha1-C/C+EltnAUrcsLCSHmLbe//hay4=", "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/accepts/node_modules/negotiator": {"version": "0.6.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/negotiator/-/negotiator-0.6.3.tgz", "integrity": "sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "8.14.1", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/address": {"version": "1.2.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/address/-/address-1.2.2.tgz", "integrity": "sha1-K1JI2sVIWmOQUyxqUX/aLj+qyJ4=", "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/ag-charts-types": {"version": "11.3.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ag-charts-types/-/ag-charts-types-11.3.2.tgz", "integrity": "sha1-sbTi0nORHN1teM57SmxWH6Inlr8=", "license": "MIT"}, "node_modules/ag-grid-community": {"version": "33.3.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ag-grid-community/-/ag-grid-community-33.3.2.tgz", "integrity": "sha1-vT5BFwlYy4HacadKMCGvZvxuvcE=", "license": "MIT", "dependencies": {"ag-charts-types": "11.3.2"}}, "node_modules/ag-grid-vue3": {"version": "33.3.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ag-grid-vue3/-/ag-grid-vue3-33.3.2.tgz", "integrity": "sha1-i8oHTlK71nK+8rZP3PJQx4T1Kz8=", "license": "MIT", "dependencies": {"ag-grid-community": "33.3.2"}, "peerDependencies": {"vue": "^3.5.0"}}, "node_modules/agent-base": {"version": "7.1.3", "license": "MIT", "engines": {"node": ">= 14"}}, "node_modules/aggregate-error": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"clean-stack": "^2.0.0", "indent-string": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/ajv": {"version": "6.12.6", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/alien-signals": {"version": "1.0.13", "dev": true, "license": "MIT"}, "node_modules/ansi-colors": {"version": "4.1.3", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ansi-escapes": {"version": "4.3.2", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/app-root-dir": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/app-root-dir/-/app-root-dir-1.0.2.tgz", "integrity": "sha1-OBh+wt6nV3//Az/8sSFyaS/24Rg=", "license": "MIT"}, "node_modules/arch": {"version": "2.2.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/arg": {"version": "5.0.2", "dev": true, "license": "MIT"}, "node_modules/argparse": {"version": "2.0.1", "license": "Python-2.0"}, "node_modules/array-flatten": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=", "license": "MIT"}, "node_modules/array-union": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/array-union/-/array-union-2.1.0.tgz", "integrity": "sha1-t5hCCtvrHego2ErNii4j0+/oXo0=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/asap": {"version": "2.0.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/asap/-/asap-2.0.6.tgz", "integrity": "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=", "license": "MIT"}, "node_modules/asn1": {"version": "0.2.6", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/assert": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/assert/-/assert-2.1.0.tgz", "integrity": "sha1-bZKiONBdwC50J8iB+4voHIRIst0=", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "is-nan": "^1.3.2", "object-is": "^1.1.5", "object.assign": "^4.1.4", "util": "^0.12.5"}}, "node_modules/assert-never": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/assert-never/-/assert-never-1.4.0.tgz", "integrity": "sha1-sNSYhijIfzXrlHFsxUQipjkn4XU=", "license": "MIT"}, "node_modules/assert-plus": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/assertion-error": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/ast-types": {"version": "0.16.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ast-types/-/ast-types-0.16.1.tgz", "integrity": "sha1-ep2hYXyQgbwSH6r+kXEbTIu4HaI=", "license": "MIT", "dependencies": {"tslib": "^2.0.1"}, "engines": {"node": ">=4"}}, "node_modules/astral-regex": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/async": {"version": "3.2.6", "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "license": "MIT"}, "node_modules/at-least-node": {"version": "1.0.0", "dev": true, "license": "ISC", "engines": {"node": ">= 4.0.0"}}, "node_modules/available-typed-arrays": {"version": "1.0.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz", "integrity": "sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=", "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/aws-sign2": {"version": "0.7.0", "dev": true, "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/aws4": {"version": "1.13.2", "dev": true, "license": "MIT"}, "node_modules/axios": {"version": "1.9.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/axios/-/axios-1.9.0.tgz", "integrity": "sha1-JVNOO3K1RUAHfTMEb3fjuNcIGQE=", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/axios/node_modules/proxy-from-env": {"version": "1.1.0", "license": "MIT"}, "node_modules/babel-walk": {"version": "3.0.0-canary-5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/babel-walk/-/babel-walk-3.0.0-canary-5.tgz", "integrity": "sha1-9m7Ncpg1eu5ElV8jWm71QhkQSxE=", "license": "MIT", "dependencies": {"@babel/types": "^7.9.6"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "license": "MIT"}, "node_modules/base64-js": {"version": "1.5.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/bcrypt-pbkdf": {"version": "1.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/better-opn": {"version": "3.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/better-opn/-/better-opn-3.0.2.tgz", "integrity": "sha1-+W813qr480FEpBAmUbq88A0diBc=", "license": "MIT", "dependencies": {"open": "^8.0.4"}, "engines": {"node": ">=12.0.0"}}, "node_modules/better-opn/node_modules/define-lazy-prop": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz", "integrity": "sha1-P3rkIRKbyqrJvHSQXJigAJ7J7n8=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/better-opn/node_modules/is-docker": {"version": "2.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-docker/-/is-docker-2.2.1.tgz", "integrity": "sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=", "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/better-opn/node_modules/is-wsl": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-wsl/-/is-wsl-2.2.0.tgz", "integrity": "sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=", "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/better-opn/node_modules/open": {"version": "8.4.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/open/-/open-8.4.2.tgz", "integrity": "sha1-W1/+Ko95Pc0qrXPlUMuHtZywhPk=", "license": "MIT", "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/big-integer": {"version": "1.6.52", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/big-integer/-/big-integer-1.6.52.tgz", "integrity": "sha1-YKiH8wR2FKjhv/5dcXNJCpfcjIU=", "license": "Unlicense", "engines": {"node": ">=0.6"}}, "node_modules/birpc": {"version": "2.3.0", "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/blob-util": {"version": "2.0.2", "dev": true, "license": "Apache-2.0"}, "node_modules/bluebird": {"version": "3.7.2", "dev": true, "license": "MIT"}, "node_modules/body-parser": {"version": "1.20.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/body-parser/-/body-parser-1.20.3.tgz", "integrity": "sha1-GVNDEiHG+1zWPEs21T+rCSjlSMY=", "license": "MIT", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.5", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.13.0", "raw-body": "2.5.2", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/body-parser/node_modules/debug": {"version": "2.6.9", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/body-parser/node_modules/iconv-lite": {"version": "0.4.24", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/body-parser/node_modules/ms": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/body-parser/node_modules/qs": {"version": "6.13.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/qs/-/qs-6.13.0.tgz", "integrity": "sha1-bKO9WEOffiRWVXmJl3h7DYilGQY=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/boolbase": {"version": "1.0.0", "dev": true, "license": "ISC"}, "node_modules/bplist-parser": {"version": "0.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/bplist-parser/-/bplist-parser-0.2.0.tgz", "integrity": "sha1-Q6nRg+W/nVRSAM6sPnEveeu+jQ4=", "license": "MIT", "dependencies": {"big-integer": "^1.6.44"}, "engines": {"node": ">= 5.10.0"}}, "node_modules/brace-expansion": {"version": "2.0.1", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/braces": {"version": "3.0.3", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browser-assert": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/browser-assert/-/browser-assert-1.2.1.tgz", "integrity": "sha1-mqpaKox0aFwq4Fv+Ru/WBvBowgA="}, "node_modules/browserslist": {"version": "4.25.0", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001718", "electron-to-chromium": "^1.5.160", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/buffer": {"version": "5.7.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/buffer-crc32": {"version": "0.2.13", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/bundle-name": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"run-applescript": "^7.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/bytes": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/bytes/-/bytes-3.1.2.tgz", "integrity": "sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/cac": {"version": "6.7.14", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cachedir": {"version": "2.4.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/call-bind": {"version": "1.0.8", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/call-bind/-/call-bind-1.0.8.tgz", "integrity": "sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/caniuse-lite": {"version": "1.0.30001720", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/caseless": {"version": "0.12.0", "dev": true, "license": "Apache-2.0"}, "node_modules/chai": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"assertion-error": "^2.0.1", "check-error": "^2.1.1", "deep-eql": "^5.0.1", "loupe": "^3.1.0", "pathval": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chalk/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/character-parser": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/character-parser/-/character-parser-2.2.0.tgz", "integrity": "sha1-x84o821LzZdE5f/CxfzeHHMmH8A=", "license": "MIT", "dependencies": {"is-regex": "^1.0.3"}}, "node_modules/check-error": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">= 16"}}, "node_modules/check-more-types": {"version": "2.24.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/chownr": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/chownr/-/chownr-3.0.0.tgz", "integrity": "sha1-mFXmTs0kCpzEJnzopKpdJKHaFeQ=", "dev": true, "license": "BlueOak-1.0.0", "engines": {"node": ">=18"}}, "node_modules/ci-info": {"version": "4.2.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/clean-stack": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/cli-cursor": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"restore-cursor": "^3.1.0"}, "engines": {"node": ">=8"}}, "node_modules/cli-table3": {"version": "0.6.1", "license": "MIT", "dependencies": {"string-width": "^4.2.0"}, "engines": {"node": "10.* || >= 12.*"}, "optionalDependencies": {"colors": "1.4.0"}}, "node_modules/cli-truncate": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"slice-ansi": "^3.0.0", "string-width": "^4.2.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/colorette": {"version": "2.0.20", "dev": true, "license": "MIT"}, "node_modules/colors": {"version": "1.4.0", "license": "MIT", "optional": true, "engines": {"node": ">=0.1.90"}}, "node_modules/combined-stream": {"version": "1.0.8", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/common-tags": {"version": "1.8.2", "dev": true, "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/commondir": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/commondir/-/commondir-1.0.1.tgz", "integrity": "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=", "license": "MIT"}, "node_modules/compressible": {"version": "2.0.18", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/compressible/-/compressible-2.0.18.tgz", "integrity": "sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=", "license": "MIT", "dependencies": {"mime-db": ">= 1.43.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/compression": {"version": "1.8.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/compression/-/compression-1.8.0.tgz", "integrity": "sha1-CUIO/JbhGg9E86VY3lnjITZBgPc=", "license": "MIT", "dependencies": {"bytes": "3.1.2", "compressible": "~2.0.18", "debug": "2.6.9", "negotiator": "~0.6.4", "on-headers": "~1.0.2", "safe-buffer": "5.2.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/compression/node_modules/debug": {"version": "2.6.9", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/compression/node_modules/ms": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/concat-map": {"version": "0.0.1", "license": "MIT"}, "node_modules/config-chain": {"version": "1.1.13", "dev": true, "license": "MIT", "dependencies": {"ini": "^1.3.4", "proto-list": "~1.2.1"}}, "node_modules/config-chain/node_modules/ini": {"version": "1.3.8", "dev": true, "license": "ISC"}, "node_modules/constantinople": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/constantinople/-/constantinople-4.0.1.tgz", "integrity": "sha1-De8RP6Dk3I3oMzGlz3nIsyUhMVE=", "license": "MIT", "dependencies": {"@babel/parser": "^7.6.0", "@babel/types": "^7.6.1"}}, "node_modules/content-disposition": {"version": "0.5.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/content-disposition/-/content-disposition-0.5.4.tgz", "integrity": "sha1-i4K076yCUSoCuwsdzsnSxejrW/4=", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/content-type/-/content-type-1.0.5.tgz", "integrity": "sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/convert-source-map": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/cookie": {"version": "0.7.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/cookie/-/cookie-0.7.1.tgz", "integrity": "sha1-L3PEIULV1c9xMQp0/ErmFnDl28k=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw=", "license": "MIT"}, "node_modules/copy-anything": {"version": "3.0.5", "license": "MIT", "dependencies": {"is-what": "^4.1.8"}, "engines": {"node": ">=12.13"}, "funding": {"url": "https://github.com/sponsors/mesqueeb"}}, "node_modules/core-util-is": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/cross-spawn": {"version": "7.0.6", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/cssesc": {"version": "3.0.0", "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/cssstyle": {"version": "4.3.1", "dev": true, "license": "MIT", "dependencies": {"@asamuzakjp/css-color": "^3.1.2", "rrweb-cssom": "^0.8.0"}, "engines": {"node": ">=18"}}, "node_modules/csstype": {"version": "3.1.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/csstype/-/csstype-3.1.3.tgz", "integrity": "sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=", "license": "MIT"}, "node_modules/cypress": {"version": "14.4.0", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"@cypress/request": "^3.0.8", "@cypress/xvfb": "^1.2.4", "@types/sinonjs__fake-timers": "8.1.1", "@types/sizzle": "^2.3.2", "arch": "^2.2.0", "blob-util": "^2.0.2", "bluebird": "^3.7.2", "buffer": "^5.7.1", "cachedir": "^2.3.0", "chalk": "^4.1.0", "check-more-types": "^2.24.0", "ci-info": "^4.1.0", "cli-cursor": "^3.1.0", "cli-table3": "0.6.1", "commander": "^6.2.1", "common-tags": "^1.8.0", "dayjs": "^1.10.4", "debug": "^4.3.4", "enquirer": "^2.3.6", "eventemitter2": "6.4.7", "execa": "4.1.0", "executable": "^4.1.1", "extract-zip": "2.0.1", "figures": "^3.2.0", "fs-extra": "^9.1.0", "getos": "^3.2.1", "is-installed-globally": "~0.4.0", "lazy-ass": "^1.6.0", "listr2": "^3.8.3", "lodash": "^4.17.21", "log-symbols": "^4.0.0", "minimist": "^1.2.8", "ospath": "^1.2.2", "pretty-bytes": "^5.6.0", "process": "^0.11.10", "proxy-from-env": "1.0.0", "request-progress": "^3.0.0", "semver": "^7.7.1", "supports-color": "^8.1.1", "tmp": "~0.2.3", "tree-kill": "1.2.2", "untildify": "^4.0.0", "yauzl": "^2.10.0"}, "bin": {"cypress": "bin/cypress"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}}, "node_modules/dashdash": {"version": "1.14.1", "dev": true, "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/data-urls": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"whatwg-mimetype": "^4.0.0", "whatwg-url": "^14.0.0"}, "engines": {"node": ">=18"}}, "node_modules/dayjs": {"version": "1.11.13", "dev": true, "license": "MIT"}, "node_modules/de-indent": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/debug": {"version": "4.4.1", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decimal.js": {"version": "10.5.0", "dev": true, "license": "MIT"}, "node_modules/deep-eql": {"version": "5.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/deep-is": {"version": "0.1.4", "dev": true, "license": "MIT"}, "node_modules/default-browser": {"version": "5.2.1", "dev": true, "license": "MIT", "dependencies": {"bundle-name": "^4.1.0", "default-browser-id": "^5.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/default-browser-id": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/define-data-property": {"version": "1.1.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/define-data-property/-/define-data-property-1.1.4.tgz", "integrity": "sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-lazy-prop": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/define-properties": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/define-properties/-/define-properties-1.2.1.tgz", "integrity": "sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=", "license": "MIT", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/delayed-stream": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/depd": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/depd/-/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/dequal": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/dequal/-/dequal-2.0.3.tgz", "integrity": "sha1-JkQhTxmX057Q7g7OcjNUkKesZ74=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/destroy": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/destroy/-/destroy-1.2.0.tgz", "integrity": "sha1-SANzVQmti+VSk0xn32FPlOZvoBU=", "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/detect-libc": {"version": "2.0.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/detect-libc/-/detect-libc-2.0.4.tgz", "integrity": "sha1-8EcVuLqBXlO02BCWVbZQimhlp+g=", "devOptional": true, "license": "Apache-2.0", "engines": {"node": ">=8"}}, "node_modules/detect-package-manager": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/detect-package-manager/-/detect-package-manager-2.0.1.tgz", "integrity": "sha1-axguOuXhgmdSv+8d6ae4KM/6UNg=", "license": "MIT", "dependencies": {"execa": "^5.1.1"}, "engines": {"node": ">=12"}}, "node_modules/detect-package-manager/node_modules/execa": {"version": "5.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/execa/-/execa-5.1.1.tgz", "integrity": "sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=", "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/detect-package-manager/node_modules/get-stream": {"version": "6.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/get-stream/-/get-stream-6.0.1.tgz", "integrity": "sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/detect-package-manager/node_modules/human-signals": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/human-signals/-/human-signals-2.1.0.tgz", "integrity": "sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=", "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/detect-port": {"version": "1.6.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/detect-port/-/detect-port-1.6.1.tgz", "integrity": "sha1-ReQHOZfF8pK5V8tnj7C7jtQlCmc=", "license": "MIT", "dependencies": {"address": "^1.0.1", "debug": "4"}, "bin": {"detect": "bin/detect-port.js", "detect-port": "bin/detect-port.js"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/dir-glob": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/dir-glob/-/dir-glob-3.0.1.tgz", "integrity": "sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=", "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/doctrine": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/doctrine/-/doctrine-3.0.0.tgz", "integrity": "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=", "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/doctypes": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/doctypes/-/doctypes-1.1.0.tgz", "integrity": "sha1-6oCxBqh1OHdOijpKWv4pPeSJ4Kk=", "license": "MIT"}, "node_modules/dotenv": {"version": "16.5.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/dotenv/-/dotenv-16.5.0.tgz", "integrity": "sha1-CStJ8l+AjwIAUAUdH/JY5ATHhpI=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/dotenv-expand": {"version": "10.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/dotenv-expand/-/dotenv-expand-10.0.0.tgz", "integrity": "sha1-EmBdAPsK9tClkuZVhYV4QDLk7zc=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}}, "node_modules/dunder-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/duplexer": {"version": "0.1.2", "dev": true, "license": "MIT"}, "node_modules/eastasianwidth": {"version": "0.2.0", "license": "MIT"}, "node_modules/ecc-jsbn": {"version": "0.1.2", "dev": true, "license": "MIT", "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "node_modules/editorconfig": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"@one-ini/wasm": "0.1.1", "commander": "^10.0.0", "minimatch": "9.0.1", "semver": "^7.5.3"}, "bin": {"editorconfig": "bin/editorconfig"}, "engines": {"node": ">=14"}}, "node_modules/editorconfig/node_modules/commander": {"version": "10.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=14"}}, "node_modules/editorconfig/node_modules/minimatch": {"version": "9.0.1", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "license": "MIT"}, "node_modules/ejs": {"version": "3.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ejs/-/ejs-3.1.10.tgz", "integrity": "sha1-aauDWLFOiW+AzDnmIIe4hQDDrDs=", "license": "Apache-2.0", "dependencies": {"jake": "^10.8.5"}, "bin": {"ejs": "bin/cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/electron-to-chromium": {"version": "1.5.161", "dev": true, "license": "ISC"}, "node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/encodeurl": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/encodeurl/-/encodeurl-2.0.0.tgz", "integrity": "sha1-e46omAd9fkCdOsRUdOo46vCFelg=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/end-of-stream": {"version": "1.4.4", "dev": true, "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/enhanced-resolve": {"version": "5.18.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/enhanced-resolve/-/enhanced-resolve-5.18.1.tgz", "integrity": "sha1-coqwgvi3toNt5R8WN6q107lWj68=", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}, "engines": {"node": ">=10.13.0"}}, "node_modules/enquirer": {"version": "2.4.1", "dev": true, "license": "MIT", "dependencies": {"ansi-colors": "^4.1.1", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8.6"}}, "node_modules/entities": {"version": "6.0.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/error-ex": {"version": "1.3.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/error-ex/-/error-ex-1.3.2.tgz", "integrity": "sha1-tKxAZIEH/c3PriQvQovqihTU8b8=", "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/error-stack-parser-es": {"version": "0.1.5", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/es-define-property": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-module-lexer": {"version": "1.7.0", "dev": true, "license": "MIT"}, "node_modules/es-object-atoms": {"version": "1.1.1", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/esbuild": {"version": "0.25.5", "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.25.5", "@esbuild/android-arm": "0.25.5", "@esbuild/android-arm64": "0.25.5", "@esbuild/android-x64": "0.25.5", "@esbuild/darwin-arm64": "0.25.5", "@esbuild/darwin-x64": "0.25.5", "@esbuild/freebsd-arm64": "0.25.5", "@esbuild/freebsd-x64": "0.25.5", "@esbuild/linux-arm": "0.25.5", "@esbuild/linux-arm64": "0.25.5", "@esbuild/linux-ia32": "0.25.5", "@esbuild/linux-loong64": "0.25.5", "@esbuild/linux-mips64el": "0.25.5", "@esbuild/linux-ppc64": "0.25.5", "@esbuild/linux-riscv64": "0.25.5", "@esbuild/linux-s390x": "0.25.5", "@esbuild/linux-x64": "0.25.5", "@esbuild/netbsd-arm64": "0.25.5", "@esbuild/netbsd-x64": "0.25.5", "@esbuild/openbsd-arm64": "0.25.5", "@esbuild/openbsd-x64": "0.25.5", "@esbuild/sunos-x64": "0.25.5", "@esbuild/win32-arm64": "0.25.5", "@esbuild/win32-ia32": "0.25.5", "@esbuild/win32-x64": "0.25.5"}}, "node_modules/esbuild-plugin-alias": {"version": "0.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/esbuild-plugin-alias/-/esbuild-plugin-alias-0.2.1.tgz", "integrity": "sha1-RahsuUHiDnwrxoor6lNWIXJJT8s=", "license": "MIT"}, "node_modules/esbuild-register": {"version": "3.6.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/esbuild-register/-/esbuild-register-3.6.0.tgz", "integrity": "sha1-zycM+md7rrvAAQrAJLgjy/cjo20=", "license": "MIT", "dependencies": {"debug": "^4.3.4"}, "peerDependencies": {"esbuild": ">=0.12 <1"}}, "node_modules/esbuild/node_modules/@esbuild/android-arm": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/android-arm/-/android-arm-0.25.5.tgz", "integrity": "sha1-QpDW00B7rjiDrSze0QgaI0RzziY=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/android-arm64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/android-arm64/-/android-arm64-0.25.5.tgz", "integrity": "sha1-vHZkB/FxiSP2uAecjGG/hqw6ak8=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/android-x64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/android-x64/-/android-x64-0.25.5.tgz", "integrity": "sha1-QMEdnLyk8kBlSMipiV0yG8OzXv8=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/darwin-x64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/darwin-x64/-/darwin-x64-0.25.5.tgz", "integrity": "sha1-4npdkqFIhu8dSS/VD8YaLU2H5Bg=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/freebsd-arm64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.5.tgz", "integrity": "sha1-l87eWdY4hAyhBOYFzbnxsRi6Cxw=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/freebsd-x64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/freebsd-x64/-/freebsd-x64-0.25.5.tgz", "integrity": "sha1-ccd4EgQqGoGQw9WB4UDRW4drnG8=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-arm": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-arm/-/linux-arm-0.25.5.tgz", "integrity": "sha1-KgvnG2zYIB+lWa6kVZjf+rwF2RE=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-arm64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-arm64/-/linux-arm64-0.25.5.tgz", "integrity": "sha1-97fI+X7/j/0uR/bGfrXJdl8hgbg=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-ia32": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-ia32/-/linux-ia32-0.25.5.tgz", "integrity": "sha1-djQURjzZ6m+h+WVV0nYvn4TGF4M=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-loong64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-loong64/-/linux-loong64-0.25.5.tgz", "integrity": "sha1-QozyIT/3hqUCpSyWzynR/PHrhQY=", "cpu": ["loong64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-mips64el": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-mips64el/-/linux-mips64el-0.25.5.tgz", "integrity": "sha1-XLzH/YQbTNUzWK/TNSfNOU4yXZY=", "cpu": ["mips64el"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-ppc64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-ppc64/-/linux-ppc64-0.25.5.tgz", "integrity": "sha1-DZVKs5zk9eUPAMT4xP04+XbBOtk=", "cpu": ["ppc64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-riscv64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-riscv64/-/linux-riscv64-0.25.5.tgz", "integrity": "sha1-Dn3TBzBQWr2AiDIehJfpS1R7+x4=", "cpu": ["riscv64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-s390x": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-s390x/-/linux-s390x-0.25.5.tgz", "integrity": "sha1-VmmvgTJ6OYozbX5A4yC1u9bm5y0=", "cpu": ["s390x"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/linux-x64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-x64/-/linux-x64-0.25.5.tgz", "integrity": "sha1-sjV90VOqSQOJZ93B/9kMaKnSoNQ=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/netbsd-x64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/netbsd-x64/-/netbsd-x64-0.25.5.tgz", "integrity": "sha1-oCBvYxTOfchxO3cycD0PWN4dHnk=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/openbsd-x64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/openbsd-x64/-/openbsd-x64-0.25.5.tgz", "integrity": "sha1-KNDNiQm3+jlTr5mPKy7TT1dnKPA=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/sunos-x64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/sunos-x64/-/sunos-x64-0.25.5.tgz", "integrity": "sha1-ooFk9bmX6CR9QH42yQ0/1d2+DcU=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["sunos"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/win32-arm64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/win32-arm64/-/win32-arm64-0.25.5.tgz", "integrity": "sha1-bq2+rTjovRL2M6UZDkXv+A4kAH4=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/win32-ia32": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/win32-ia32/-/win32-ia32-0.25.5.tgz", "integrity": "sha1-urYogAVIL57Srbne1+iOuppizA0=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/esbuild/node_modules/@esbuild/win32-x64": {"version": "0.25.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/win32-x64/-/win32-x64-0.25.5.tgz", "integrity": "sha1-f8EUr19lY/GfczJLXV/zbs4IA9E=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/escalade": {"version": "3.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/escodegen": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/escodegen/-/escodegen-2.1.0.tgz", "integrity": "sha1-upO7t6Q5htKdYEH5n1Ji2nc+Lhc=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esprima": "^4.0.1", "estraverse": "^5.2.0", "esutils": "^2.0.2"}, "bin": {"escodegen": "bin/escodegen.js", "esgenerate": "bin/esgenerate.js"}, "engines": {"node": ">=6.0"}, "optionalDependencies": {"source-map": "~0.6.1"}}, "node_modules/eslint": {"version": "9.28.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.12.1", "@eslint/config-array": "^0.20.0", "@eslint/config-helpers": "^0.2.1", "@eslint/core": "^0.14.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "9.28.0", "@eslint/plugin-kit": "^0.3.1", "@humanfs/node": "^0.16.6", "@humanwhocodes/module-importer": "^1.0.1", "@humanwhocodes/retry": "^0.4.2", "@types/estree": "^1.0.6", "@types/json-schema": "^7.0.15", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.6", "debug": "^4.3.2", "escape-string-regexp": "^4.0.0", "eslint-scope": "^8.3.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.5.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^8.0.0", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "json-stable-stringify-without-jsonify": "^1.0.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}, "peerDependencies": {"jiti": "*"}, "peerDependenciesMeta": {"jiti": {"optional": true}}}, "node_modules/eslint-config-prettier": {"version": "10.1.5", "dev": true, "license": "MIT", "bin": {"eslint-config-prettier": "bin/cli.js"}, "funding": {"url": "https://opencollective.com/eslint-config-prettier"}, "peerDependencies": {"eslint": ">=7.0.0"}}, "node_modules/eslint-plugin-cypress": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"globals": "^15.15.0"}, "peerDependencies": {"eslint": ">=9"}}, "node_modules/eslint-plugin-cypress/node_modules/globals": {"version": "15.15.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint-plugin-prettier": {"version": "5.4.1", "dev": true, "license": "MIT", "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.11.7"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint-plugin-prettier"}, "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": ">= 7.0.0 <10.0.0 || >=10.1.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}}, "node_modules/eslint-plugin-vue": {"version": "10.0.1", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.4.0", "natural-compare": "^1.4.0", "nth-check": "^2.1.1", "postcss-selector-parser": "^6.0.15", "semver": "^7.6.3", "xml-name-validator": "^4.0.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "vue-eslint-parser": "^10.0.0"}}, "node_modules/eslint-scope": {"version": "8.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-utils": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/eslint-utils/-/eslint-utils-2.1.0.tgz", "integrity": "sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=", "license": "MIT", "dependencies": {"eslint-visitor-keys": "^1.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/mysticatea"}}, "node_modules/eslint-utils/node_modules/eslint-visitor-keys": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=", "license": "Apache-2.0", "engines": {"node": ">=4"}}, "node_modules/eslint-visitor-keys": {"version": "3.4.3", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/eslint/node_modules/eslint-visitor-keys": {"version": "4.2.0", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/esm-resolve": {"version": "1.0.11", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/esm-resolve/-/esm-resolve-1.0.11.tgz", "integrity": "sha1-k/ACHVwG+5vtd/zQEOud5UU44ds=", "license": "Apache-2.0"}, "node_modules/espree": {"version": "10.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.14.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^4.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/espree/node_modules/eslint-visitor-keys": {"version": "4.2.0", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esprima": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/esprima/-/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=", "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/esquery": {"version": "1.6.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/estree-walker": {"version": "2.0.2", "license": "MIT"}, "node_modules/esutils": {"version": "2.0.3", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.8.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/event-stream": {"version": "3.3.4", "dev": true, "license": "MIT", "dependencies": {"duplexer": "~0.1.1", "from": "~0", "map-stream": "~0.1.0", "pause-stream": "0.0.11", "split": "0.3", "stream-combiner": "~0.0.4", "through": "~2.3.1"}}, "node_modules/eventemitter2": {"version": "6.4.7", "dev": true, "license": "MIT"}, "node_modules/events": {"version": "3.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/events/-/events-3.3.0.tgz", "integrity": "sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=", "license": "MIT", "engines": {"node": ">=0.8.x"}}, "node_modules/execa": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.0", "get-stream": "^5.0.0", "human-signals": "^1.1.1", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "onetime": "^5.1.0", "signal-exit": "^3.0.2", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/executable": {"version": "4.1.1", "dev": true, "license": "MIT", "dependencies": {"pify": "^2.2.0"}, "engines": {"node": ">=4"}}, "node_modules/expect-type": {"version": "1.2.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.0.0"}}, "node_modules/express": {"version": "4.21.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/express/-/express-4.21.2.tgz", "integrity": "sha1-zyUOSDYhdOrWzqSlZqvvAWLB7DI=", "license": "MIT", "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.3", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.7.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.3.1", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.3", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.12", "proxy-addr": "~2.0.7", "qs": "6.13.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.19.0", "serve-static": "1.16.2", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/express/node_modules/debug": {"version": "2.6.9", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/express/node_modules/ms": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/express/node_modules/qs": {"version": "6.13.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/qs/-/qs-6.13.0.tgz", "integrity": "sha1-bKO9WEOffiRWVXmJl3h7DYilGQY=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/extend": {"version": "3.0.2", "dev": true, "license": "MIT"}, "node_modules/extract-zip": {"version": "2.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"debug": "^4.1.1", "get-stream": "^5.1.0", "yauzl": "^2.10.0"}, "bin": {"extract-zip": "cli.js"}, "engines": {"node": ">= 10.17.0"}, "optionalDependencies": {"@types/yauzl": "^2.9.1"}}, "node_modules/extsprintf": {"version": "1.3.0", "dev": true, "engines": ["node >=0.6.0"], "license": "MIT"}, "node_modules/fast-deep-equal": {"version": "3.1.3", "dev": true, "license": "MIT"}, "node_modules/fast-diff": {"version": "1.3.0", "dev": true, "license": "Apache-2.0"}, "node_modules/fast-glob": {"version": "3.3.3", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/fast-xml-parser": {"version": "5.2.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fast-xml-parser/-/fast-xml-parser-5.2.3.tgz", "integrity": "sha1-zW01OZiFcxO5LH+6Xl2KZoArARE=", "funding": [{"type": "github", "url": "https://github.com/sponsors/NaturalIntelligence"}], "license": "MIT", "dependencies": {"strnum": "^2.1.0"}, "bin": {"fxparser": "src/cli/cli.js"}}, "node_modules/fastq": {"version": "1.19.1", "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fd-slicer": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"pend": "~1.2.0"}}, "node_modules/fetch-retry": {"version": "5.0.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fetch-retry/-/fetch-retry-5.0.6.tgz", "integrity": "sha1-F9C8kEI0Bbeoi3Q1W/NkrNKn+lY=", "license": "MIT"}, "node_modules/figures": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^1.0.5"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/figures/node_modules/escape-string-regexp": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/file-entry-cache": {"version": "8.0.0", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^4.0.0"}, "engines": {"node": ">=16.0.0"}}, "node_modules/file-system-cache": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/file-system-cache/-/file-system-cache-2.3.0.tgz", "integrity": "sha1-IB/q9MjNl7nQ1gjpaGG7YAX0b+Y=", "license": "MIT", "dependencies": {"fs-extra": "11.1.1", "ramda": "0.29.0"}}, "node_modules/file-system-cache/node_modules/fs-extra": {"version": "11.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fs-extra/-/fs-extra-11.1.1.tgz", "integrity": "sha1-2mn3w587ACN4sJVLtq5+/cCHbi0=", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/filelist": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/filelist/-/filelist-1.0.4.tgz", "integrity": "sha1-94l4oelEd1/55i50RCTyFeWDUrU=", "license": "Apache-2.0", "dependencies": {"minimatch": "^5.0.1"}}, "node_modules/filelist/node_modules/minimatch": {"version": "5.1.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/minimatch/-/minimatch-5.1.6.tgz", "integrity": "sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY=", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/fill-range": {"version": "7.1.1", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "1.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/finalhandler/-/finalhandler-1.3.1.tgz", "integrity": "sha1-DFdfHR0yTd0do1rX7OPffRkIgBk=", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/debug": {"version": "2.6.9", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/finalhandler/node_modules/ms": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/find-cache-dir": {"version": "3.3.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/find-cache-dir/-/find-cache-dir-3.3.2.tgz", "integrity": "sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks=", "license": "MIT", "dependencies": {"commondir": "^1.0.1", "make-dir": "^3.0.2", "pkg-dir": "^4.1.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/avajs/find-cache-dir?sponsor=1"}}, "node_modules/find-cache-dir/node_modules/find-up": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/find-up/-/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/find-cache-dir/node_modules/locate-path": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/find-cache-dir/node_modules/p-limit": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/find-cache-dir/node_modules/p-locate": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/find-cache-dir/node_modules/pkg-dir": {"version": "4.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pkg-dir/-/pkg-dir-4.2.0.tgz", "integrity": "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=", "license": "MIT", "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/find-up": {"version": "5.0.0", "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.4"}, "engines": {"node": ">=16"}}, "node_modules/flatted": {"version": "3.3.3", "dev": true, "license": "ISC"}, "node_modules/follow-redirects": {"version": "1.15.9", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/for-each": {"version": "0.3.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/for-each/-/for-each-0.3.5.tgz", "integrity": "sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=", "license": "MIT", "dependencies": {"is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/foreground-child": {"version": "3.3.1", "license": "ISC", "dependencies": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/foreground-child/node_modules/signal-exit": {"version": "4.1.0", "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/forever-agent": {"version": "0.6.1", "dev": true, "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/form-data": {"version": "4.0.2", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/forwarded": {"version": "0.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/forwarded/-/forwarded-0.2.0.tgz", "integrity": "sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fresh": {"version": "0.5.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/from": {"version": "0.1.7", "dev": true, "license": "MIT"}, "node_modules/fs-extra": {"version": "9.1.0", "dev": true, "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/fsevents": {"version": "2.3.3", "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-stream": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/getos": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"async": "^3.2.0"}}, "node_modules/getpass": {"version": "0.1.7", "dev": true, "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/glob": {"version": "10.4.5", "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/glob-to-regexp": {"version": "0.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz", "integrity": "sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4=", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/global-dirs": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"ini": "2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globals": {"version": "14.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globby": {"version": "11.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/globby/-/globby-11.1.0.tgz", "integrity": "sha1-vUvpi7BC+D15b344EZkfvoKg00s=", "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/gopd": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "node_modules/graphemer": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/handlebars": {"version": "4.7.8", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/handlebars/-/handlebars-4.7.8.tgz", "integrity": "sha1-QcQsGLG+I2VDkYjHfGr65xwM2ek=", "license": "MIT", "dependencies": {"minimist": "^1.2.5", "neo-async": "^2.6.2", "source-map": "^0.6.1", "wordwrap": "^1.0.0"}, "bin": {"handlebars": "bin/handlebars"}, "engines": {"node": ">=0.4.7"}, "optionalDependencies": {"uglify-js": "^3.1.4"}}, "node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz", "integrity": "sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hash-sum": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/hash-sum/-/hash-sum-2.0.0.tgz", "integrity": "sha1-gdAbtd6OpKIUrV1urRtSNGCwtFo=", "license": "MIT"}, "node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/he": {"version": "1.2.0", "dev": true, "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/hookable": {"version": "5.5.3", "license": "MIT"}, "node_modules/hosted-git-info": {"version": "2.8.9", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/hosted-git-info/-/hosted-git-info-2.8.9.tgz", "integrity": "sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=", "license": "ISC"}, "node_modules/html-encoding-sniffer": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"whatwg-encoding": "^3.1.1"}, "engines": {"node": ">=18"}}, "node_modules/http-errors": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/http-errors/-/http-errors-2.0.0.tgz", "integrity": "sha1-t3dKFIbvc892Z6ya4IWMASxXudM=", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-proxy-agent": {"version": "7.0.2", "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/http-signature": {"version": "1.4.0", "dev": true, "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "jsprim": "^2.0.2", "sshpk": "^1.18.0"}, "engines": {"node": ">=0.10"}}, "node_modules/https-proxy-agent": {"version": "7.0.6", "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "node_modules/human-signals": {"version": "1.1.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=8.12.0"}}, "node_modules/ibantools": {"version": "4.5.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ibantools/-/ibantools-4.5.1.tgz", "integrity": "sha1-rSDTZPfZTHPmxP3zqy/rXziXTNU=", "license": "MIT or MPL-2.0"}, "node_modules/iconv-lite": {"version": "0.6.3", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ignore": {"version": "5.3.2", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/import-fresh": {"version": "3.3.1", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/indent-string": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/inherits/-/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "license": "ISC"}, "node_modules/ini": {"version": "2.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-arguments": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-arguments/-/is-arguments-1.2.0.tgz", "integrity": "sha1-rVjGrs9WO3jvK/BN9UDaj119jhs=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arrayish": {"version": "0.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-arrayish/-/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "license": "MIT"}, "node_modules/is-callable": {"version": "1.2.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-callable/-/is-callable-1.2.7.tgz", "integrity": "sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-core-module": {"version": "2.16.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-core-module/-/is-core-module-2.16.1.tgz", "integrity": "sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-docker": {"version": "3.0.0", "dev": true, "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-expression": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-expression/-/is-expression-4.0.0.tgz", "integrity": "sha1-wzFVliq/IdCv0lUlFNZ9LsFv0qs=", "license": "MIT", "dependencies": {"acorn": "^7.1.1", "object-assign": "^4.1.1"}}, "node_modules/is-expression/node_modules/acorn": {"version": "7.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/acorn/-/acorn-7.4.1.tgz", "integrity": "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-generator-function": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-generator-function/-/is-generator-function-1.1.0.tgz", "integrity": "sha1-vz7tqTEgE5T1e126KAD5GiODCco=", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-proto": "^1.0.0", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-inside-container": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"is-docker": "^3.0.0"}, "bin": {"is-inside-container": "cli.js"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-installed-globally": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"global-dirs": "^3.0.0", "is-path-inside": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-nan": {"version": "1.3.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-nan/-/is-nan-1.3.2.tgz", "integrity": "sha1-BDpUreoxdItVts1OCara+mm9nh0=", "license": "MIT", "dependencies": {"call-bind": "^1.0.0", "define-properties": "^1.1.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-number": {"version": "7.0.0", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-path-inside": {"version": "3.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-plain-obj": {"version": "4.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-potential-custom-element-name": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/is-promise": {"version": "2.2.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-promise/-/is-promise-2.2.2.tgz", "integrity": "sha1-OauVnMv5p3TPB597QMeib3YxNfE=", "license": "MIT"}, "node_modules/is-regex": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-regex/-/is-regex-1.2.1.tgz", "integrity": "sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-stream": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-typed-array": {"version": "1.1.15", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-typed-array/-/is-typed-array-1.1.15.tgz", "integrity": "sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=", "license": "MIT", "dependencies": {"which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typedarray": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/is-unicode-supported": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-what": {"version": "4.1.16", "license": "MIT", "engines": {"node": ">=12.13"}, "funding": {"url": "https://github.com/sponsors/mesqueeb"}}, "node_modules/is-wsl": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"is-inside-container": "^1.0.0"}, "engines": {"node": ">=16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/isexe": {"version": "2.0.0", "license": "ISC"}, "node_modules/isstream": {"version": "0.1.2", "dev": true, "license": "MIT"}, "node_modules/jackspeak": {"version": "3.4.3", "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/jake": {"version": "10.9.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/jake/-/jake-10.9.2.tgz", "integrity": "sha1-auSH5qaa/sOl4WdiiZa1nzWuK38=", "license": "Apache-2.0", "dependencies": {"async": "^3.2.3", "chalk": "^4.0.2", "filelist": "^1.0.4", "minimatch": "^3.1.2"}, "bin": {"jake": "bin/cli.js"}, "engines": {"node": ">=10"}}, "node_modules/jake/node_modules/brace-expansion": {"version": "1.1.11", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/jake/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/jiti": {"version": "2.4.2", "dev": true, "license": "MIT", "bin": {"jiti": "lib/jiti-cli.mjs"}}, "node_modules/joi": {"version": "17.13.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.5", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}}, "node_modules/js-beautify": {"version": "1.15.4", "dev": true, "license": "MIT", "dependencies": {"config-chain": "^1.1.13", "editorconfig": "^1.0.4", "glob": "^10.4.2", "js-cookie": "^3.0.5", "nopt": "^7.2.1"}, "bin": {"css-beautify": "js/bin/css-beautify.js", "html-beautify": "js/bin/html-beautify.js", "js-beautify": "js/bin/js-beautify.js"}, "engines": {"node": ">=14"}}, "node_modules/js-cookie": {"version": "3.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=14"}}, "node_modules/js-stringify": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/js-stringify/-/js-stringify-1.0.2.tgz", "integrity": "sha1-Fzb939lyTyijaCrcYjCufk6Weds=", "license": "MIT"}, "node_modules/js-tokens": {"version": "4.0.0", "license": "MIT"}, "node_modules/js-yaml": {"version": "4.1.0", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsbn": {"version": "0.1.1", "dev": true, "license": "MIT"}, "node_modules/jsdom": {"version": "26.1.0", "dev": true, "license": "MIT", "dependencies": {"cssstyle": "^4.2.1", "data-urls": "^5.0.0", "decimal.js": "^10.5.0", "html-encoding-sniffer": "^4.0.0", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "is-potential-custom-element-name": "^1.0.1", "nwsapi": "^2.2.16", "parse5": "^7.2.1", "rrweb-cssom": "^0.8.0", "saxes": "^6.0.0", "symbol-tree": "^3.2.4", "tough-cookie": "^5.1.1", "w3c-xmlserializer": "^5.0.0", "webidl-conversions": "^7.0.0", "whatwg-encoding": "^3.1.1", "whatwg-mimetype": "^4.0.0", "whatwg-url": "^14.1.1", "ws": "^8.18.0", "xml-name-validator": "^5.0.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"canvas": "^3.0.0"}, "peerDependenciesMeta": {"canvas": {"optional": true}}}, "node_modules/jsdom/node_modules/xml-name-validator": {"version": "5.0.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18"}}, "node_modules/jsesc": {"version": "3.1.0", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json-buffer": {"version": "3.0.1", "dev": true, "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": "^18.17.0 || >=20.5.0"}}, "node_modules/json-schema": {"version": "0.4.0", "dev": true, "license": "(AFL-2.1 OR BSD-3-Clause)"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/json-stringify-safe": {"version": "5.0.1", "dev": true, "license": "ISC"}, "node_modules/json5": {"version": "2.2.3", "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonc-eslint-parser": {"version": "1.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/jsonc-eslint-parser/-/jsonc-eslint-parser-1.4.1.tgz", "integrity": "sha1-jL6Z9vUZmsvFqCPEwLYTVBECf6Y=", "license": "MIT", "dependencies": {"acorn": "^7.4.1", "eslint-utils": "^2.1.0", "eslint-visitor-keys": "^1.3.0", "espree": "^6.0.0", "semver": "^6.3.0"}, "engines": {"node": ">=8.10.0"}}, "node_modules/jsonc-eslint-parser/node_modules/acorn": {"version": "7.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/acorn/-/acorn-7.4.1.tgz", "integrity": "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/jsonc-eslint-parser/node_modules/eslint-visitor-keys": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=", "license": "Apache-2.0", "engines": {"node": ">=4"}}, "node_modules/jsonc-eslint-parser/node_modules/espree": {"version": "6.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/espree/-/espree-6.2.1.tgz", "integrity": "sha1-d/xy4f10SiBSwg84pbV1gy6Cc0o=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^7.1.1", "acorn-jsx": "^5.2.0", "eslint-visitor-keys": "^1.1.0"}, "engines": {"node": ">=6.0.0"}}, "node_modules/jsonc-eslint-parser/node_modules/semver": {"version": "6.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/semver/-/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/jsonfile": {"version": "6.1.0", "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsprim": {"version": "2.0.2", "dev": true, "engines": ["node >=0.6.0"], "license": "MIT", "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.4.0", "verror": "1.10.0"}}, "node_modules/jstransformer": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/jstransformer/-/jstransformer-1.0.0.tgz", "integrity": "sha1-7Yvwkh4vPx7U1cGkT2hwntJHIsM=", "license": "MIT", "dependencies": {"is-promise": "^2.0.0", "promise": "^7.0.1"}}, "node_modules/keyv": {"version": "4.5.4", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/kleur": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/kleur/-/kleur-3.0.3.tgz", "integrity": "sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/kolorist": {"version": "1.8.0", "dev": true, "license": "MIT"}, "node_modules/lazy-ass": {"version": "1.6.0", "dev": true, "license": "MIT", "engines": {"node": "> 0.8"}}, "node_modules/lazy-universal-dotenv": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lazy-universal-dotenv/-/lazy-universal-dotenv-4.0.0.tgz", "integrity": "sha1-CyIMJk6JoEKjcYGkkozdKYr3NCI=", "license": "Apache-2.0", "dependencies": {"app-root-dir": "^1.0.2", "dotenv": "^16.0.0", "dotenv-expand": "^10.0.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/levn": {"version": "0.4.1", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/lightningcss": {"version": "1.30.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lightningcss/-/lightningcss-1.30.1.tgz", "integrity": "sha1-eOl5wtWVv8uQ0qjA62Mv5sW/7V0=", "devOptional": true, "license": "MPL-2.0", "dependencies": {"detect-libc": "^2.0.3"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"lightningcss-darwin-arm64": "1.30.1", "lightningcss-darwin-x64": "1.30.1", "lightningcss-freebsd-x64": "1.30.1", "lightningcss-linux-arm-gnueabihf": "1.30.1", "lightningcss-linux-arm64-gnu": "1.30.1", "lightningcss-linux-arm64-musl": "1.30.1", "lightningcss-linux-x64-gnu": "1.30.1", "lightningcss-linux-x64-musl": "1.30.1", "lightningcss-win32-arm64-msvc": "1.30.1", "lightningcss-win32-x64-msvc": "1.30.1"}}, "node_modules/lightningcss-darwin-arm64": {"version": "1.30.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.30.1.tgz", "integrity": "sha1-PUfOXiIblWfHA5UO3yUpyko3AK4=", "cpu": ["arm64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["darwin"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-darwin-x64": {"version": "1.30.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.30.1.tgz", "integrity": "sha1-6BEF0/1jMIYMFf6GD2TTnP9fvSI=", "cpu": ["x64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["darwin"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-freebsd-x64": {"version": "1.30.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.30.1.tgz", "integrity": "sha1-oOcyAxCD/51iXF2wIdCesIWvi+Q=", "cpu": ["x64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["freebsd"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-linux-arm-gnueabihf": {"version": "1.30.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.30.1.tgz", "integrity": "sha1-H17MpglVKN22SfkwS6JWDHJHSQg=", "cpu": ["arm"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-linux-arm64-gnu": {"version": "1.30.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.30.1.tgz", "integrity": "sha1-7ud5lyYQO///HoiZPfcm9pEewAk=", "cpu": ["arm64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-linux-arm64-musl": {"version": "1.30.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.30.1.tgz", "integrity": "sha1-8uS1P0KJL+7vj2IMu4iffAZKff4=", "cpu": ["arm64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-linux-x64-gnu": {"version": "1.30.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lightningcss-linux-x64-gnu/-/lightningcss-linux-x64-gnu-1.30.1.tgz", "integrity": "sha1-L8cJYiS8AA67l+6pSuokjFsOsVc=", "cpu": ["x64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-linux-x64-musl": {"version": "1.30.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.30.1.tgz", "integrity": "sha1-ZtyisVn9gZ6oMsRIldB+WzHXXyY=", "cpu": ["x64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["linux"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-win32-arm64-msvc": {"version": "1.30.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.30.1.tgz", "integrity": "sha1-fYEQoZ18LSK/3y8ruL5o59G2kDk=", "cpu": ["arm64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["win32"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lightningcss-win32-x64-msvc": {"version": "1.30.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lightningcss-win32-x64-msvc/-/lightningcss-win32-x64-msvc-1.30.1.tgz", "integrity": "sha1-/X3QCOqYSUuF0ktL6gFnk/Lg41I=", "cpu": ["x64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["win32"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "integrity": "sha1-7KKE910pZQeTCdwK2SVauy68FjI=", "license": "MIT"}, "node_modules/listr2": {"version": "3.14.0", "dev": true, "license": "MIT", "dependencies": {"cli-truncate": "^2.1.0", "colorette": "^2.0.16", "log-update": "^4.0.0", "p-map": "^4.0.0", "rfdc": "^1.3.0", "rxjs": "^7.5.1", "through": "^2.3.8", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=10.0.0"}, "peerDependencies": {"enquirer": ">= 2.3.0 < 3"}, "peerDependenciesMeta": {"enquirer": {"optional": true}}}, "node_modules/listr2/node_modules/wrap-ansi": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/locate-path": {"version": "6.0.0", "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "dev": true, "license": "MIT"}, "node_modules/lodash.once": {"version": "4.1.1", "dev": true, "license": "MIT"}, "node_modules/log-symbols": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-update": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-escapes": "^4.3.0", "cli-cursor": "^3.1.0", "slice-ansi": "^4.0.0", "wrap-ansi": "^6.2.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-update/node_modules/slice-ansi": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/log-update/node_modules/wrap-ansi": {"version": "6.2.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/loupe": {"version": "3.1.3", "dev": true, "license": "MIT"}, "node_modules/lru-cache": {"version": "10.4.3", "license": "ISC"}, "node_modules/magic-string": {"version": "0.30.17", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "node_modules/make-dir": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/make-dir/-/make-dir-3.1.0.tgz", "integrity": "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=", "license": "MIT", "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/make-dir/node_modules/semver": {"version": "6.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/semver/-/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/map-or-similar": {"version": "1.5.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/map-or-similar/-/map-or-similar-1.5.0.tgz", "integrity": "sha1-beJlMXSt+12e3DPGnT6Sobdvrwg=", "license": "MIT"}, "node_modules/map-stream": {"version": "0.1.0", "dev": true}, "node_modules/math-intrinsics": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/memoizerific": {"version": "1.11.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/memoizerific/-/memoizerific-1.11.3.tgz", "integrity": "sha1-fIekZGREwy11Q4VwkF8tvRsagFo=", "license": "MIT", "dependencies": {"map-or-similar": "^1.5.0"}}, "node_modules/memorystream": {"version": "0.3.1", "dev": true, "engines": {"node": ">= 0.10.0"}}, "node_modules/merge-descriptors": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/merge-descriptors/-/merge-descriptors-1.0.3.tgz", "integrity": "sha1-2AMZpl88eTU1Hlz9rI+TGFBNvtU=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/merge-stream": {"version": "2.0.0", "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/methods": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "4.0.8", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "1.6.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/mime/-/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/minimatch": {"version": "9.0.5", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/minimist": {"version": "1.2.8", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "7.1.2", "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/minizlib": {"version": "3.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/minizlib/-/minizlib-3.0.2.tgz", "integrity": "sha1-8z1jjrJ59mRDmqONxfkWB0aMtXQ=", "dev": true, "license": "MIT", "dependencies": {"minipass": "^7.1.2"}, "engines": {"node": ">= 18"}}, "node_modules/mitt": {"version": "3.0.1", "license": "MIT"}, "node_modules/mkdirp": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/mkdirp/-/mkdirp-3.0.1.tgz", "integrity": "sha1-5E5MVgf7J5wWgkFxPMbg/qmty1A=", "dev": true, "license": "MIT", "bin": {"mkdirp": "dist/cjs/src/bin.js"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/moment": {"version": "2.30.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/moment/-/moment-2.30.1.tgz", "integrity": "sha1-+MkcB7enhuMMWZJt9TC06slpdK4=", "license": "MIT", "engines": {"node": "*"}}, "node_modules/mrmime": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/muggle-string": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.11", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/natural-compare": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/negotiator": {"version": "0.6.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/negotiator/-/negotiator-0.6.4.tgz", "integrity": "sha1-d3lI4kUmUcVwtxLdAcI+JicT//c=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/neo-async": {"version": "2.6.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/neo-async/-/neo-async-2.6.2.tgz", "integrity": "sha1-tKr7k+OustgXTKU88WOrfXMIMF8=", "license": "MIT"}, "node_modules/node-fetch": {"version": "2.7.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/node-fetch/-/node-fetch-2.7.0.tgz", "integrity": "sha1-0PD6bj4twdJ+/NitmdVQvalNGH0=", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-fetch/node_modules/tr46": {"version": "0.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/tr46/-/tr46-0.0.3.tgz", "integrity": "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=", "license": "MIT"}, "node_modules/node-fetch/node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/node-fetch/node_modules/whatwg-url": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha1-lmRU6HZUYuN2RNNib2dCzotwll0=", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/node-releases": {"version": "2.0.19", "dev": true, "license": "MIT"}, "node_modules/nopt": {"version": "7.2.1", "dev": true, "license": "ISC", "dependencies": {"abbrev": "^2.0.0"}, "bin": {"nopt": "bin/nopt.js"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/normalize-package-data": {"version": "2.5.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/normalize-package-data/-/normalize-package-data-2.5.0.tgz", "integrity": "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "node_modules/normalize-package-data/node_modules/semver": {"version": "5.7.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/semver/-/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=", "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/npm-normalize-package-bin": {"version": "4.0.0", "dev": true, "license": "ISC", "engines": {"node": "^18.17.0 || >=20.5.0"}}, "node_modules/npm-run-all2": {"version": "7.0.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.2.1", "cross-spawn": "^7.0.6", "memorystream": "^0.3.1", "minimatch": "^9.0.0", "pidtree": "^0.6.0", "read-package-json-fast": "^4.0.0", "shell-quote": "^1.7.3", "which": "^5.0.0"}, "bin": {"npm-run-all": "bin/npm-run-all/index.js", "npm-run-all2": "bin/npm-run-all/index.js", "run-p": "bin/run-p/index.js", "run-s": "bin/run-s/index.js"}, "engines": {"node": "^18.17.0 || >=20.5.0", "npm": ">= 9"}}, "node_modules/npm-run-all2/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/npm-run-all2/node_modules/isexe": {"version": "3.1.1", "dev": true, "license": "ISC", "engines": {"node": ">=16"}}, "node_modules/npm-run-all2/node_modules/which": {"version": "5.0.0", "dev": true, "license": "ISC", "dependencies": {"isexe": "^3.1.1"}, "bin": {"node-which": "bin/which.js"}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "node_modules/npm-run-path": {"version": "4.0.1", "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/nth-check": {"version": "2.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "node_modules/nwsapi": {"version": "2.2.20", "dev": true, "license": "MIT"}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.13.4", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-is": {"version": "1.1.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/object-is/-/object-is-1.1.6.tgz", "integrity": "sha1-GmpTrtLdj35ndf+HC+pYVFlWqwc=", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/object-keys/-/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object.assign": {"version": "4.1.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/object.assign/-/object.assign-4.1.7.tgz", "integrity": "sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/on-finished": {"version": "2.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/on-finished/-/on-finished-2.4.1.tgz", "integrity": "sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/on-headers/-/on-headers-1.0.2.tgz", "integrity": "sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/open": {"version": "10.1.2", "dev": true, "license": "MIT", "dependencies": {"default-browser": "^5.2.1", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0", "is-wsl": "^3.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/optionator": {"version": "0.9.4", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/ospath": {"version": "1.2.2", "dev": true, "license": "MIT"}, "node_modules/p-limit": {"version": "3.1.0", "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-map": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"aggregate-error": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-try": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/p-try/-/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/package-json-from-dist": {"version": "1.0.1", "license": "BlueOak-1.0.0"}, "node_modules/parent-module": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parlem-webcomponents-common": {"version": "1.1.242", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/parlem-webcomponents-common/-/parlem-webcomponents-common-1.1.242.tgz", "integrity": "sha1-AZ2z2dzDzA7iOrt8kRrp5Xo/7P4=", "dependencies": {"@fortawesome/fontawesome-svg-core": "6.7.2", "@fortawesome/free-brands-svg-icons": "6.7.2", "@fortawesome/free-regular-svg-icons": "6.7.2", "@fortawesome/free-solid-svg-icons": "6.7.2", "@fortawesome/vue-fontawesome": "3.0.8", "@intlify/unplugin-vue-i18n": "^0.10.1", "@storybook/vue3-vite": "^7.6.20", "@tailwindcss/container-queries": "^0.1.1", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "ibantools": "^4.5.1", "moment": "^2.30.1", "postcss": "^8.5.6", "postcss-nesting": "^11.3.0", "rollup": "^3.29.5", "vue": "^3.5.18", "vue-i18n": "^9.14.5"}, "peerDependencies": {"vue": "^3.4.21"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/aix-ppc64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/aix-ppc64/-/aix-ppc64-0.21.5.tgz", "integrity": "sha1-xxhKMmUz/N8bjuBzPiHHE7l1V18=", "cpu": ["ppc64"], "license": "MIT", "optional": true, "os": ["aix"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/android-arm": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/android-arm/-/android-arm-0.21.5.tgz", "integrity": "sha1-mwQ4T7dxkm36bXrQQyTssqubLig=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["android"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/android-arm64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/android-arm64/-/android-arm64-0.21.5.tgz", "integrity": "sha1-Cdm0NXeA2p6jp9+4M6Hx/0ObQFI=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["android"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/android-x64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/android-x64/-/android-x64-0.21.5.tgz", "integrity": "sha1-KZGOwtt1TO3LbBsE3ozWVHr2Rh4=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["android"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/darwin-arm64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/darwin-arm64/-/darwin-arm64-0.21.5.tgz", "integrity": "sha1-5JW1OWYOUWkPOSivUKdvsKbM/yo=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["darwin"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/darwin-x64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/darwin-x64/-/darwin-x64-0.21.5.tgz", "integrity": "sha1-wTg4+lc3KDmr3dyR1xVCzuouHiI=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["darwin"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/freebsd-arm64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.5.tgz", "integrity": "sha1-ZGuYmqIL+J/Qcd1dv61po1QuVQ4=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["freebsd"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/freebsd-x64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/freebsd-x64/-/freebsd-x64-0.21.5.tgz", "integrity": "sha1-qmFc/ICvlU00WJBuOMoiwYz1wmE=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["freebsd"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/linux-arm": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-arm/-/linux-arm-0.21.5.tgz", "integrity": "sha1-/G/RGorKVsH284lPK+oEefj2Jrk=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["linux"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/linux-arm64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-arm64/-/linux-arm64-0.21.5.tgz", "integrity": "sha1-cKxvoU9ct+H3+Ie8/7aArQmSK1s=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/linux-ia32": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-ia32/-/linux-ia32-0.21.5.tgz", "integrity": "sha1-MnH1Oz+T49CT1RjRZJ1taNNG7eI=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["linux"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/linux-loong64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-loong64/-/linux-loong64-0.21.5.tgz", "integrity": "sha1-7WLgQjjFcCauqDHFoTC3PA+fJt8=", "cpu": ["loong64"], "license": "MIT", "optional": true, "os": ["linux"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/linux-mips64el": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-mips64el/-/linux-mips64el-0.21.5.tgz", "integrity": "sha1-55uOtIvzsQb63sGsgkD7l7TmTL4=", "cpu": ["mips64el"], "license": "MIT", "optional": true, "os": ["linux"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/linux-ppc64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-ppc64/-/linux-ppc64-0.21.5.tgz", "integrity": "sha1-XyIDhgoUO5kZ04PvdXNSH7FUw+Q=", "cpu": ["ppc64"], "license": "MIT", "optional": true, "os": ["linux"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/linux-riscv64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-riscv64/-/linux-riscv64-0.21.5.tgz", "integrity": "sha1-B7yv2ZMi1a9i9hjLnmqbf0u4Jdw=", "cpu": ["riscv64"], "license": "MIT", "optional": true, "os": ["linux"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/linux-s390x": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-s390x/-/linux-s390x-0.21.5.tgz", "integrity": "sha1-t8z2hnUdaj5EuGJ6uryL4+9i2N4=", "cpu": ["s390x"], "license": "MIT", "optional": true, "os": ["linux"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/linux-x64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-x64/-/linux-x64-0.21.5.tgz", "integrity": "sha1-bY8Mdo4HDmQwmvgAS7lOaKsrs7A=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/netbsd-x64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/netbsd-x64/-/netbsd-x64-0.21.5.tgz", "integrity": "sha1-u+Qw9g03jsuI3sshnGAmZzh6YEc=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["netbsd"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/openbsd-x64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/openbsd-x64/-/openbsd-x64-0.21.5.tgz", "integrity": "sha1-mdHPKTcnlWDSEEgh9czOIgyyr3A=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["openbsd"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/sunos-x64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/sunos-x64/-/sunos-x64-0.21.5.tgz", "integrity": "sha1-CHQVEsENUpVmurqDe0/gUsjzSHs=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["sunos"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/win32-arm64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/win32-arm64/-/win32-arm64-0.21.5.tgz", "integrity": "sha1-Z1tzhTmEESQHNQFhRKsumaYPx10=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["win32"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/win32-ia32": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/win32-ia32/-/win32-ia32-0.21.5.tgz", "integrity": "sha1-G/w86YqmypoJaeTSr3IUTFnBGTs=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["win32"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@esbuild/win32-x64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/win32-x64/-/win32-x64-0.21.5.tgz", "integrity": "sha1-rK01HVgtFXuxRVNdsqb/U91RS1w=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"], "peer": true, "engines": {"node": ">=12"}}, "node_modules/parlem-webcomponents-common/node_modules/@intlify/core-base": {"version": "9.14.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/core-base/-/core-base-9.14.5.tgz", "integrity": "sha1-z+1jHpizT09v4TEubALdis4YAZc=", "license": "MIT", "dependencies": {"@intlify/message-compiler": "9.14.5", "@intlify/shared": "9.14.5"}, "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}}, "node_modules/parlem-webcomponents-common/node_modules/@intlify/message-compiler": {"version": "9.14.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/message-compiler/-/message-compiler-9.14.5.tgz", "integrity": "sha1-A7CjWMk8su7ZWTZTHIpYcf04QbA=", "license": "MIT", "dependencies": {"@intlify/shared": "9.14.5", "source-map-js": "^1.0.2"}, "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}}, "node_modules/parlem-webcomponents-common/node_modules/@intlify/shared": {"version": "9.14.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/shared/-/shared-9.14.5.tgz", "integrity": "sha1-LkIZO4NW2yDuKMFdHubI8P2mwLk=", "license": "MIT", "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}}, "node_modules/parlem-webcomponents-common/node_modules/@storybook/builder-vite": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/builder-vite/-/builder-vite-7.6.20.tgz", "integrity": "sha1-TkbmWGQASa/Mu3XSj/UqcgIO240=", "license": "MIT", "dependencies": {"@storybook/channels": "7.6.20", "@storybook/client-logger": "7.6.20", "@storybook/core-common": "7.6.20", "@storybook/csf-plugin": "7.6.20", "@storybook/node-logger": "7.6.20", "@storybook/preview": "7.6.20", "@storybook/preview-api": "7.6.20", "@storybook/types": "7.6.20", "@types/find-cache-dir": "^3.2.1", "browser-assert": "^1.2.1", "es-module-lexer": "^0.9.3", "express": "^4.17.3", "find-cache-dir": "^3.0.0", "fs-extra": "^11.1.0", "magic-string": "^0.30.0", "rollup": "^2.25.0 || ^3.3.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "peerDependencies": {"@preact/preset-vite": "*", "typescript": ">= 4.3.x", "vite": "^3.0.0 || ^4.0.0 || ^5.0.0", "vite-plugin-glimmerx": "*"}, "peerDependenciesMeta": {"@preact/preset-vite": {"optional": true}, "typescript": {"optional": true}, "vite-plugin-glimmerx": {"optional": true}}}, "node_modules/parlem-webcomponents-common/node_modules/@storybook/vue3-vite": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/vue3-vite/-/vue3-vite-7.6.20.tgz", "integrity": "sha1-5Tneq5idzptzXDYRdcU46f9O2tg=", "license": "MIT", "dependencies": {"@storybook/builder-vite": "7.6.20", "@storybook/core-server": "7.6.20", "@storybook/vue3": "7.6.20", "@vitejs/plugin-vue": "^4.0.0", "magic-string": "^0.30.0", "vue-docgen-api": "^4.40.0"}, "engines": {"node": "^14.18 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "peerDependencies": {"vite": "^3.0.0 || ^4.0.0 || ^5.0.0"}}, "node_modules/parlem-webcomponents-common/node_modules/@vitejs/plugin-vue": {"version": "4.6.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vitejs/plugin-vue/-/plugin-vue-4.6.2.tgz", "integrity": "sha1-BX0t7ZTE5xuU6YFPktzZMGMXqkY=", "license": "MIT", "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependencies": {"vite": "^4.0.0 || ^5.0.0", "vue": "^3.2.25"}}, "node_modules/parlem-webcomponents-common/node_modules/@vue/devtools-api": {"version": "6.6.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/devtools-api/-/devtools-api-6.6.4.tgz", "integrity": "sha1-y+l/4BYrNl7cHbqA4XP5BJJTU0M=", "license": "MIT"}, "node_modules/parlem-webcomponents-common/node_modules/es-module-lexer": {"version": "0.9.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/es-module-lexer/-/es-module-lexer-0.9.3.tgz", "integrity": "sha1-bxPbAMw4QXE32vdDZvU1yOtDjxk=", "license": "MIT"}, "node_modules/parlem-webcomponents-common/node_modules/esbuild": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/esbuild/-/esbuild-0.21.5.tgz", "integrity": "sha1-nKMBsSCSKVm3ZjYNisgw2g0CmX0=", "hasInstallScript": true, "license": "MIT", "peer": true, "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.21.5", "@esbuild/android-arm": "0.21.5", "@esbuild/android-arm64": "0.21.5", "@esbuild/android-x64": "0.21.5", "@esbuild/darwin-arm64": "0.21.5", "@esbuild/darwin-x64": "0.21.5", "@esbuild/freebsd-arm64": "0.21.5", "@esbuild/freebsd-x64": "0.21.5", "@esbuild/linux-arm": "0.21.5", "@esbuild/linux-arm64": "0.21.5", "@esbuild/linux-ia32": "0.21.5", "@esbuild/linux-loong64": "0.21.5", "@esbuild/linux-mips64el": "0.21.5", "@esbuild/linux-ppc64": "0.21.5", "@esbuild/linux-riscv64": "0.21.5", "@esbuild/linux-s390x": "0.21.5", "@esbuild/linux-x64": "0.21.5", "@esbuild/netbsd-x64": "0.21.5", "@esbuild/openbsd-x64": "0.21.5", "@esbuild/sunos-x64": "0.21.5", "@esbuild/win32-arm64": "0.21.5", "@esbuild/win32-ia32": "0.21.5", "@esbuild/win32-x64": "0.21.5"}}, "node_modules/parlem-webcomponents-common/node_modules/fs-extra": {"version": "11.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fs-extra/-/fs-extra-11.3.0.tgz", "integrity": "sha1-DaztE2u69lpVWjJnGa+TGtx6MU0=", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/parlem-webcomponents-common/node_modules/rollup": {"version": "3.29.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/rollup/-/rollup-3.29.5.tgz", "integrity": "sha1-ii5HenWLUg+3ja8EvKTFIsHailQ=", "license": "MIT", "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/parlem-webcomponents-common/node_modules/vite": {"version": "5.4.19", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vite/-/vite-5.4.19.tgz", "integrity": "sha1-IO/QYEEARLPtVVBJQYpefRmY+Vk=", "license": "MIT", "peer": true, "dependencies": {"esbuild": "^0.21.3", "postcss": "^8.4.43", "rollup": "^4.20.0"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^18.0.0 || >=20.0.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "sass-embedded": "*", "stylus": "*", "sugarss": "*", "terser": "^5.4.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}}}, "node_modules/parlem-webcomponents-common/node_modules/vite/node_modules/rollup": {"version": "4.41.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/rollup/-/rollup-4.41.1.tgz", "integrity": "sha1-Rt3BszzxsLqpkyDTsLSXPcIlO2o=", "license": "MIT", "peer": true, "dependencies": {"@types/estree": "1.0.7"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.41.1", "@rollup/rollup-android-arm64": "4.41.1", "@rollup/rollup-darwin-arm64": "4.41.1", "@rollup/rollup-darwin-x64": "4.41.1", "@rollup/rollup-freebsd-arm64": "4.41.1", "@rollup/rollup-freebsd-x64": "4.41.1", "@rollup/rollup-linux-arm-gnueabihf": "4.41.1", "@rollup/rollup-linux-arm-musleabihf": "4.41.1", "@rollup/rollup-linux-arm64-gnu": "4.41.1", "@rollup/rollup-linux-arm64-musl": "4.41.1", "@rollup/rollup-linux-loongarch64-gnu": "4.41.1", "@rollup/rollup-linux-powerpc64le-gnu": "4.41.1", "@rollup/rollup-linux-riscv64-gnu": "4.41.1", "@rollup/rollup-linux-riscv64-musl": "4.41.1", "@rollup/rollup-linux-s390x-gnu": "4.41.1", "@rollup/rollup-linux-x64-gnu": "4.41.1", "@rollup/rollup-linux-x64-musl": "4.41.1", "@rollup/rollup-win32-arm64-msvc": "4.41.1", "@rollup/rollup-win32-ia32-msvc": "4.41.1", "@rollup/rollup-win32-x64-msvc": "4.41.1", "fsevents": "~2.3.2"}}, "node_modules/parlem-webcomponents-common/node_modules/vue-i18n": {"version": "9.14.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-i18n/-/vue-i18n-9.14.5.tgz", "integrity": "sha1-cvv0OEuDocWeyeAf9dMCJL2RUM8=", "license": "MIT", "dependencies": {"@intlify/core-base": "9.14.5", "@intlify/shared": "9.14.5", "@vue/devtools-api": "^6.5.0"}, "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}, "peerDependencies": {"vue": "^3.0.0"}}, "node_modules/parse-json": {"version": "5.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/parse-json/-/parse-json-5.2.0.tgz", "integrity": "sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parse-json/node_modules/json-parse-even-better-errors": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=", "license": "MIT"}, "node_modules/parse-ms": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parse5": {"version": "7.3.0", "dev": true, "license": "MIT", "dependencies": {"entities": "^6.0.0"}, "funding": {"url": "https://github.com/inikulin/parse5?sponsor=1"}}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-browserify": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/path-exists": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-key": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=", "license": "MIT"}, "node_modules/path-scurry": {"version": "1.11.1", "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/path-to-regexp": {"version": "0.1.12", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/path-to-regexp/-/path-to-regexp-0.1.12.tgz", "integrity": "sha1-1eGhLkeKl21DLvPFjVNLmSMWS7c=", "license": "MIT"}, "node_modules/path-type": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/path-type/-/path-type-4.0.0.tgz", "integrity": "sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/pathe": {"version": "2.0.3", "dev": true, "license": "MIT"}, "node_modules/pathval": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 14.16"}}, "node_modules/pause-stream": {"version": "0.0.11", "dev": true, "license": ["MIT", "Apache2"], "dependencies": {"through": "~2.3"}}, "node_modules/pend": {"version": "1.2.0", "dev": true, "license": "MIT"}, "node_modules/perfect-debounce": {"version": "1.0.0", "license": "MIT"}, "node_modules/performance-now": {"version": "2.1.0", "dev": true, "license": "MIT"}, "node_modules/picocolors": {"version": "1.1.1", "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pidtree": {"version": "0.6.0", "dev": true, "license": "MIT", "bin": {"pidtree": "bin/pidtree.js"}, "engines": {"node": ">=0.10"}}, "node_modules/pify": {"version": "2.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/pinia": {"version": "3.0.2", "license": "MIT", "dependencies": {"@vue/devtools-api": "^7.7.2"}, "funding": {"url": "https://github.com/sponsors/posva"}, "peerDependencies": {"typescript": ">=4.4.4", "vue": "^2.7.0 || ^3.5.11"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/pkg-dir": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pkg-dir/-/pkg-dir-5.0.0.tgz", "integrity": "sha1-oC1q6+a6EzqSj3Suwguv3+a452A=", "license": "MIT", "dependencies": {"find-up": "^5.0.0"}, "engines": {"node": ">=10"}}, "node_modules/possible-typed-array-names": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz", "integrity": "sha1-k+NYK8DlQmWG2dB7ee5A/IQd5K4=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/postcss": {"version": "8.5.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/postcss/-/postcss-8.5.6.tgz", "integrity": "sha1-KCUAZhWmGbT2Kp50JswSCzSajzw=", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-nesting": {"version": "11.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/postcss-nesting/-/postcss-nesting-11.3.0.tgz", "integrity": "sha1-8KFuzVVVV3DUGXQjbJCRhEc1Wl8=", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "CC0-1.0", "dependencies": {"@csstools/selector-specificity": "^2.0.0", "postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^14 || ^16 || >=18"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/postcss-selector-parser": {"version": "6.1.2", "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/prelude-ls": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/prettier": {"version": "3.5.3", "dev": true, "license": "MIT", "bin": {"prettier": "bin/prettier.cjs"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/prettier-linter-helpers": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"fast-diff": "^1.1.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/pretty-bytes": {"version": "5.6.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pretty-hrtime": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pretty-hrtime/-/pretty-hrtime-1.0.3.tgz", "integrity": "sha1-t+PqQkNaTJsnWdmeDyAesZWALuE=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/pretty-ms": {"version": "9.2.0", "dev": true, "license": "MIT", "dependencies": {"parse-ms": "^4.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/process": {"version": "0.11.10", "license": "MIT", "engines": {"node": ">= 0.6.0"}}, "node_modules/promise": {"version": "7.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/promise/-/promise-7.3.1.tgz", "integrity": "sha1-BktyYCsY+Q8pGSuLG8QY/9Hr078=", "license": "MIT", "dependencies": {"asap": "~2.0.3"}}, "node_modules/prompts": {"version": "2.4.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/prompts/-/prompts-2.4.2.tgz", "integrity": "sha1-e1fnOzpIAprRDr1E90sBcipMsGk=", "license": "MIT", "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "engines": {"node": ">= 6"}}, "node_modules/proto-list": {"version": "1.2.4", "dev": true, "license": "ISC"}, "node_modules/proxy-addr": {"version": "2.0.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/proxy-addr/-/proxy-addr-2.0.7.tgz", "integrity": "sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/proxy-from-env": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/ps-tree": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"event-stream": "=3.3.4"}, "bin": {"ps-tree": "bin/ps-tree.js"}, "engines": {"node": ">= 0.10"}}, "node_modules/pug": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug/-/pug-3.0.3.tgz", "integrity": "sha1-4YMkoxTNAiiDseA3K4rzoamfdZc=", "license": "MIT", "dependencies": {"pug-code-gen": "^3.0.3", "pug-filters": "^4.0.0", "pug-lexer": "^5.0.1", "pug-linker": "^4.0.0", "pug-load": "^3.0.0", "pug-parser": "^6.0.0", "pug-runtime": "^3.0.1", "pug-strip-comments": "^2.0.0"}}, "node_modules/pug-attrs": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-attrs/-/pug-attrs-3.0.0.tgz", "integrity": "sha1-sQRR4DSBZeMfrRzCPr3dncc0fEE=", "license": "MIT", "dependencies": {"constantinople": "^4.0.1", "js-stringify": "^1.0.2", "pug-runtime": "^3.0.0"}}, "node_modules/pug-code-gen": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-code-gen/-/pug-code-gen-3.0.3.tgz", "integrity": "sha1-WBMxeMtCP+Fxauzhwdo5KnUlFSA=", "license": "MIT", "dependencies": {"constantinople": "^4.0.1", "doctypes": "^1.1.0", "js-stringify": "^1.0.2", "pug-attrs": "^3.0.0", "pug-error": "^2.1.0", "pug-runtime": "^3.0.1", "void-elements": "^3.1.0", "with": "^7.0.0"}}, "node_modules/pug-error": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-error/-/pug-error-2.1.0.tgz", "integrity": "sha1-F+o3tYe2RD1LjxSDdOwntUtAblU=", "license": "MIT"}, "node_modules/pug-filters": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-filters/-/pug-filters-4.0.0.tgz", "integrity": "sha1-0+Sa9bqEcum3pm2YDnB86dLMm14=", "license": "MIT", "dependencies": {"constantinople": "^4.0.1", "jstransformer": "1.0.0", "pug-error": "^2.0.0", "pug-walk": "^2.0.0", "resolve": "^1.15.1"}}, "node_modules/pug-lexer": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-lexer/-/pug-lexer-5.0.1.tgz", "integrity": "sha1-rkRijFvvmxkLZlaDsojKkCS4sNU=", "license": "MIT", "dependencies": {"character-parser": "^2.2.0", "is-expression": "^4.0.0", "pug-error": "^2.0.0"}}, "node_modules/pug-linker": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-linker/-/pug-linker-4.0.0.tgz", "integrity": "sha1-EsvAWU/Fo+Brn8Web5PBRpYqdwg=", "license": "MIT", "dependencies": {"pug-error": "^2.0.0", "pug-walk": "^2.0.0"}}, "node_modules/pug-load": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-load/-/pug-load-3.0.0.tgz", "integrity": "sha1-n9nNpSICsIrbEdJWgfufNL1BtmI=", "license": "MIT", "dependencies": {"object-assign": "^4.1.1", "pug-walk": "^2.0.0"}}, "node_modules/pug-parser": {"version": "6.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-parser/-/pug-parser-6.0.0.tgz", "integrity": "sha1-qP3ANYY6lbLB3F6/Ts+AtOdqEmA=", "license": "MIT", "dependencies": {"pug-error": "^2.0.0", "token-stream": "1.0.0"}}, "node_modules/pug-runtime": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-runtime/-/pug-runtime-3.0.1.tgz", "integrity": "sha1-9jaXYgRyPzWoxfb61qzaKhkbg9c=", "license": "MIT"}, "node_modules/pug-strip-comments": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-strip-comments/-/pug-strip-comments-2.0.0.tgz", "integrity": "sha1-+UsH/WtJVSMzD0kKf1VLT/h2MD4=", "license": "MIT", "dependencies": {"pug-error": "^2.0.0"}}, "node_modules/pug-walk": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-walk/-/pug-walk-2.0.0.tgz", "integrity": "sha1-QXqrwpIyu0SZtbUGmistKiTV9f4=", "license": "MIT"}, "node_modules/pump": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.14.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/queue-microtask": {"version": "1.2.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/ramda": {"version": "0.29.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ramda/-/ramda-0.29.0.tgz", "integrity": "sha1-+7tnp0CnVMiky7QeKm4OuFB/Vfs=", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/ramda"}}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.5.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/raw-body/-/raw-body-2.5.2.tgz", "integrity": "sha1-mf69g7kOCJdQh+jx+UGaFJNmtoo=", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/raw-body/node_modules/iconv-lite": {"version": "0.4.24", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/read-package-json-fast": {"version": "4.0.0", "dev": true, "license": "ISC", "dependencies": {"json-parse-even-better-errors": "^4.0.0", "npm-normalize-package-bin": "^4.0.0"}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "node_modules/read-pkg": {"version": "5.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/read-pkg/-/read-pkg-5.2.0.tgz", "integrity": "sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=", "license": "MIT", "dependencies": {"@types/normalize-package-data": "^2.4.0", "normalize-package-data": "^2.5.0", "parse-json": "^5.0.0", "type-fest": "^0.6.0"}, "engines": {"node": ">=8"}}, "node_modules/read-pkg-up": {"version": "7.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/read-pkg-up/-/read-pkg-up-7.0.1.tgz", "integrity": "sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc=", "license": "MIT", "dependencies": {"find-up": "^4.1.0", "read-pkg": "^5.2.0", "type-fest": "^0.8.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/read-pkg-up/node_modules/find-up": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/find-up/-/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/read-pkg-up/node_modules/locate-path": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/read-pkg-up/node_modules/p-limit": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/read-pkg-up/node_modules/p-locate": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/read-pkg-up/node_modules/type-fest": {"version": "0.8.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/type-fest/-/type-fest-0.8.1.tgz", "integrity": "sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=8"}}, "node_modules/read-pkg/node_modules/type-fest": {"version": "0.6.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/type-fest/-/type-fest-0.6.0.tgz", "integrity": "sha1-jSojcNPfiG61yQraHFv2GIrPg4s=", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=8"}}, "node_modules/recast": {"version": "0.23.11", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/recast/-/recast-0.23.11.tgz", "integrity": "sha1-iIVXC7KM93O6HcYA2n9QL3iD9z8=", "license": "MIT", "dependencies": {"ast-types": "^0.16.1", "esprima": "~4.0.0", "source-map": "~0.6.1", "tiny-invariant": "^1.3.3", "tslib": "^2.0.1"}, "engines": {"node": ">= 4"}}, "node_modules/request-progress": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"throttleit": "^1.0.0"}}, "node_modules/resolve": {"version": "1.22.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/resolve/-/resolve-1.22.10.tgz", "integrity": "sha1-tmPoP/sJu/I4aURza6roAwKbizk=", "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/restore-cursor": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=8"}}, "node_modules/reusify": {"version": "1.1.0", "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rfdc": {"version": "1.4.1", "license": "MIT"}, "node_modules/rollup": {"version": "4.41.1", "devOptional": true, "license": "MIT", "dependencies": {"@types/estree": "1.0.7"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.41.1", "@rollup/rollup-android-arm64": "4.41.1", "@rollup/rollup-darwin-arm64": "4.41.1", "@rollup/rollup-darwin-x64": "4.41.1", "@rollup/rollup-freebsd-arm64": "4.41.1", "@rollup/rollup-freebsd-x64": "4.41.1", "@rollup/rollup-linux-arm-gnueabihf": "4.41.1", "@rollup/rollup-linux-arm-musleabihf": "4.41.1", "@rollup/rollup-linux-arm64-gnu": "4.41.1", "@rollup/rollup-linux-arm64-musl": "4.41.1", "@rollup/rollup-linux-loongarch64-gnu": "4.41.1", "@rollup/rollup-linux-powerpc64le-gnu": "4.41.1", "@rollup/rollup-linux-riscv64-gnu": "4.41.1", "@rollup/rollup-linux-riscv64-musl": "4.41.1", "@rollup/rollup-linux-s390x-gnu": "4.41.1", "@rollup/rollup-linux-x64-gnu": "4.41.1", "@rollup/rollup-linux-x64-musl": "4.41.1", "@rollup/rollup-win32-arm64-msvc": "4.41.1", "@rollup/rollup-win32-ia32-msvc": "4.41.1", "@rollup/rollup-win32-x64-msvc": "4.41.1", "fsevents": "~2.3.2"}}, "node_modules/rrweb-cssom": {"version": "0.8.0", "dev": true, "license": "MIT"}, "node_modules/run-applescript": {"version": "7.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/run-parallel": {"version": "1.2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/rxjs": {"version": "7.8.2", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-regex-test": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/safe-regex-test/-/safe-regex-test-1.1.0.tgz", "integrity": "sha1-f4fftnoxUHguqvGFg/9dFxGsEME=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safer-buffer": {"version": "2.1.2", "license": "MIT"}, "node_modules/sax": {"version": "1.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/sax/-/sax-1.4.1.tgz", "integrity": "sha1-RMyJiDd/EmME07P8EBDHM7kp7w8=", "license": "ISC"}, "node_modules/saxes": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"xmlchars": "^2.2.0"}, "engines": {"node": ">=v12.22.7"}}, "node_modules/semver": {"version": "7.7.2", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/send": {"version": "0.19.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/send/-/send-0.19.0.tgz", "integrity": "sha1-u8WjiMjqbASJZwSdvqwOSj8J1/g=", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/debug": {"version": "2.6.9", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/send/node_modules/debug/node_modules/ms": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/send/node_modules/encodeurl": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/serve-static": {"version": "1.16.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/serve-static/-/serve-static-1.16.2.tgz", "integrity": "sha1-tqU0PaR/a90mc4SL9FdUlB6AMpY=", "license": "MIT", "dependencies": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/set-function-length": {"version": "1.2.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/set-function-length/-/set-function-length-1.2.2.tgz", "integrity": "sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/setprototypeof": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/setprototypeof/-/setprototypeof-1.2.0.tgz", "integrity": "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=", "license": "ISC"}, "node_modules/shebang-command": {"version": "2.0.0", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/shell-quote": {"version": "1.8.3", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel": {"version": "1.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/siginfo": {"version": "2.0.0", "dev": true, "license": "ISC"}, "node_modules/signal-exit": {"version": "3.0.7", "license": "ISC"}, "node_modules/sirv": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"@polka/url": "^1.0.0-next.24", "mrmime": "^2.0.0", "totalist": "^3.0.0"}, "engines": {"node": ">=18"}}, "node_modules/sisteransi": {"version": "1.0.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/sisteransi/-/sisteransi-1.0.5.tgz", "integrity": "sha1-E01oEpd1ZDfMBcoBNw06elcQde0=", "license": "MIT"}, "node_modules/slash": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/slash/-/slash-3.0.0.tgz", "integrity": "sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/slice-ansi": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/source-map": {"version": "0.6.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.2.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/spdx-correct": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/spdx-correct/-/spdx-correct-3.2.0.tgz", "integrity": "sha1-T1qwZo8AWeNPnADc4zF4ShLeTpw=", "license": "Apache-2.0", "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-exceptions": {"version": "2.5.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/spdx-exceptions/-/spdx-exceptions-2.5.0.tgz", "integrity": "sha1-XWB9J/yAb2bXtkp2ZlD6iQ8E7WY=", "license": "CC-BY-3.0"}, "node_modules/spdx-expression-parse": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz", "integrity": "sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=", "license": "MIT", "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-license-ids": {"version": "3.0.21", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/spdx-license-ids/-/spdx-license-ids-3.0.21.tgz", "integrity": "sha1-bW6YDJ3ytvyQU0OjstcCpiOVNsM=", "license": "CC0-1.0"}, "node_modules/speakingurl": {"version": "14.0.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/split": {"version": "0.3.3", "dev": true, "license": "MIT", "dependencies": {"through": "2"}, "engines": {"node": "*"}}, "node_modules/sshpk": {"version": "1.18.0", "dev": true, "license": "MIT", "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}}, "node_modules/stackback": {"version": "0.0.2", "dev": true, "license": "MIT"}, "node_modules/start-server-and-test": {"version": "2.0.12", "dev": true, "license": "MIT", "dependencies": {"arg": "^5.0.2", "bluebird": "3.7.2", "check-more-types": "2.24.0", "debug": "4.4.1", "execa": "5.1.1", "lazy-ass": "1.6.0", "ps-tree": "1.2.0", "wait-on": "8.0.3"}, "bin": {"server-test": "src/bin/start.js", "start-server-and-test": "src/bin/start.js", "start-test": "src/bin/start.js"}, "engines": {"node": ">=16"}}, "node_modules/start-server-and-test/node_modules/execa": {"version": "5.1.1", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/start-server-and-test/node_modules/get-stream": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/start-server-and-test/node_modules/human-signals": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/statuses": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/statuses/-/statuses-2.0.1.tgz", "integrity": "sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/std-env": {"version": "3.9.0", "dev": true, "license": "MIT"}, "node_modules/stream-combiner": {"version": "0.0.4", "dev": true, "license": "MIT", "dependencies": {"duplexer": "~0.1.1"}}, "node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/strnum": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/strnum/-/strnum-2.1.1.tgz", "integrity": "sha1-zypuDPkDcouLLEuXG342tOgtRqs=", "funding": [{"type": "github", "url": "https://github.com/sponsors/NaturalIntelligence"}], "license": "MIT"}, "node_modules/superjson": {"version": "2.2.2", "license": "MIT", "dependencies": {"copy-anything": "^3.0.2"}, "engines": {"node": ">=16"}}, "node_modules/supports-color": {"version": "8.1.1", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha1-btpL00SjyUrqN21MwxvHcxEDngk=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/symbol-tree": {"version": "3.2.4", "dev": true, "license": "MIT"}, "node_modules/synchronous-promise": {"version": "2.0.17", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/synchronous-promise/-/synchronous-promise-2.0.17.tgz", "integrity": "sha1-OJATGWMvlGyYIVJYbyyvjdwlwDI=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/synckit": {"version": "0.11.8", "dev": true, "license": "MIT", "dependencies": {"@pkgr/core": "^0.2.4"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/synckit"}}, "node_modules/tailwindcss": {"version": "4.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/tailwindcss/-/tailwindcss-4.1.10.tgz", "integrity": "sha1-UVdBsKeTFtGXHRgvf7xDW2hnk3M=", "license": "MIT"}, "node_modules/tapable": {"version": "2.2.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/tapable/-/tapable-2.2.2.tgz", "integrity": "sha1-q0mENA0wy5mJpJADLwhtu4tW2HI=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/tar": {"version": "7.4.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/tar/-/tar-7.4.3.tgz", "integrity": "sha1-iLvpKGo/zZAOlFks2noisZLoBXE=", "dev": true, "license": "ISC", "dependencies": {"@isaacs/fs-minipass": "^4.0.0", "chownr": "^3.0.0", "minipass": "^7.1.2", "minizlib": "^3.0.1", "mkdirp": "^3.0.1", "yallist": "^5.0.0"}, "engines": {"node": ">=18"}}, "node_modules/tar/node_modules/yallist": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/yallist/-/yallist-5.0.0.tgz", "integrity": "sha1-AOLeRDY57Q14/YfeDSdGn7z/tTM=", "dev": true, "license": "BlueOak-1.0.0", "engines": {"node": ">=18"}}, "node_modules/telejson": {"version": "7.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/telejson/-/telejson-7.2.0.tgz", "integrity": "sha1-OZT2yaj41/Lbqb4sfFu7RH6HbzI=", "license": "MIT", "dependencies": {"memoizerific": "^1.11.3"}}, "node_modules/throttleit": {"version": "1.0.1", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/through": {"version": "2.3.8", "dev": true, "license": "MIT"}, "node_modules/tiny-invariant": {"version": "1.3.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/tiny-invariant/-/tiny-invariant-1.3.3.tgz", "integrity": "sha1-RmgLeoc6DV0QAFmV65CnDXTWASc=", "license": "MIT"}, "node_modules/tinybench": {"version": "2.9.0", "dev": true, "license": "MIT"}, "node_modules/tinyexec": {"version": "0.3.2", "dev": true, "license": "MIT"}, "node_modules/tinyglobby": {"version": "0.2.14", "dev": true, "license": "MIT", "dependencies": {"fdir": "^6.4.4", "picomatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}}, "node_modules/tinyglobby/node_modules/fdir": {"version": "6.4.5", "dev": true, "license": "MIT", "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/tinyglobby/node_modules/picomatch": {"version": "4.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/tinypool": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": "^18.0.0 || >=20.0.0"}}, "node_modules/tinyrainbow": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/tinyspy": {"version": "4.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/tldts": {"version": "6.1.86", "dev": true, "license": "MIT", "dependencies": {"tldts-core": "^6.1.86"}, "bin": {"tldts": "bin/cli.js"}}, "node_modules/tldts-core": {"version": "6.1.86", "dev": true, "license": "MIT"}, "node_modules/tmp": {"version": "0.2.3", "dev": true, "license": "MIT", "engines": {"node": ">=14.14"}}, "node_modules/to-regex-range": {"version": "5.0.1", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/toidentifier/-/toidentifier-1.0.1.tgz", "integrity": "sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/token-stream": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/token-stream/-/token-stream-1.0.0.tgz", "integrity": "sha1-zCAOqyYT9BZtJ/+a/HylbUnfbrQ=", "license": "MIT"}, "node_modules/totalist": {"version": "3.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/tough-cookie": {"version": "5.1.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tldts": "^6.1.32"}, "engines": {"node": ">=16"}}, "node_modules/tr46": {"version": "5.1.1", "dev": true, "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "engines": {"node": ">=18"}}, "node_modules/tree-kill": {"version": "1.2.2", "dev": true, "license": "MIT", "bin": {"tree-kill": "cli.js"}}, "node_modules/ts-api-utils": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=18.12"}, "peerDependencies": {"typescript": ">=4.8.4"}}, "node_modules/ts-dedent": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ts-dedent/-/ts-dedent-2.2.0.tgz", "integrity": "sha1-OeS9KXzQNikq4jlOs0Er5j9WO7U=", "license": "MIT", "engines": {"node": ">=6.10"}}, "node_modules/ts-map": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ts-map/-/ts-map-1.0.3.tgz", "integrity": "sha1-HE0hjeyBPSEDt+BOS880jhRxwf8=", "license": "MIT"}, "node_modules/tslib": {"version": "2.8.1", "license": "0BSD"}, "node_modules/tunnel": {"version": "0.0.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/tunnel/-/tunnel-0.0.6.tgz", "integrity": "sha1-cvExSzSlsZLbASMk3yzFh8pH+Sw=", "license": "MIT", "engines": {"node": ">=0.6.11 <=0.7.0 || >=0.7.3"}}, "node_modules/tunnel-agent": {"version": "0.6.0", "dev": true, "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/tweetnacl": {"version": "0.14.5", "dev": true, "license": "Unlicense"}, "node_modules/type-check": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-fest": {"version": "0.21.3", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/type-is": {"version": "1.6.18", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/type-is/-/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typescript": {"version": "5.8.3", "devOptional": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/typescript-eslint": {"version": "8.33.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/eslint-plugin": "8.33.0", "@typescript-eslint/parser": "8.33.0", "@typescript-eslint/utils": "8.33.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/uglify-js": {"version": "3.19.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/uglify-js/-/uglify-js-3.19.3.tgz", "integrity": "sha1-gjFem7xvKyWIiFis0f/4RBA1t38=", "license": "BSD-2-<PERSON><PERSON>", "optional": true, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.8.0"}}, "node_modules/undici-types": {"version": "6.21.0", "license": "MIT"}, "node_modules/unicorn-magic": {"version": "0.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/universalify": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/unplugin": {"version": "1.16.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/unplugin/-/unplugin-1.16.1.tgz", "integrity": "sha1-qETS48OxSkrClFxCvoBAkyG2EZk=", "license": "MIT", "dependencies": {"acorn": "^8.14.0", "webpack-virtual-modules": "^0.6.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/untildify": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uri-js": {"version": "4.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/util": {"version": "0.12.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/util/-/util-0.12.5.tgz", "integrity": "sha1-XxemBZtz22GodWaHgaHCsTa9b7w=", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "is-arguments": "^1.0.4", "is-generator-function": "^1.0.7", "is-typed-array": "^1.1.3", "which-typed-array": "^1.1.2"}}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "node_modules/utils-merge": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "8.3.2", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/validate-npm-package-license": {"version": "3.0.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz", "integrity": "sha1-/JH2uce6FchX9MssXe/uw51PQQo=", "license": "Apache-2.0", "dependencies": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/verror": {"version": "1.10.0", "dev": true, "engines": ["node >=0.6.0"], "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "node_modules/vite": {"version": "6.3.5", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.25.0", "fdir": "^6.4.4", "picomatch": "^4.0.2", "postcss": "^8.5.3", "rollup": "^4.34.9", "tinyglobby": "^0.2.13"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0", "jiti": ">=1.21.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "sass-embedded": "*", "stylus": "*", "sugarss": "*", "terser": "^5.16.0", "tsx": "^4.8.1", "yaml": "^2.4.2"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "jiti": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}, "tsx": {"optional": true}, "yaml": {"optional": true}}}, "node_modules/vite-hot-client": {"version": "2.0.4", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"vite": "^2.6.0 || ^3.0.0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0"}}, "node_modules/vite-node": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"cac": "^6.7.14", "debug": "^4.4.1", "es-module-lexer": "^1.7.0", "pathe": "^2.0.3", "vite": "^5.0.0 || ^6.0.0 || ^7.0.0-0"}, "bin": {"vite-node": "vite-node.mjs"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/vite-plugin-inspect": {"version": "0.8.9", "dev": true, "license": "MIT", "dependencies": {"@antfu/utils": "^0.7.10", "@rollup/pluginutils": "^5.1.3", "debug": "^4.3.7", "error-stack-parser-es": "^0.1.5", "fs-extra": "^11.2.0", "open": "^10.1.0", "perfect-debounce": "^1.0.0", "picocolors": "^1.1.1", "sirv": "^3.0.0"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"vite": "^3.1.0 || ^4.0.0 || ^5.0.0-0 || ^6.0.1"}, "peerDependenciesMeta": {"@nuxt/kit": {"optional": true}}}, "node_modules/vite-plugin-inspect/node_modules/fs-extra": {"version": "11.3.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/vite-plugin-vue-devtools": {"version": "7.7.6", "dev": true, "license": "MIT", "dependencies": {"@vue/devtools-core": "^7.7.6", "@vue/devtools-kit": "^7.7.6", "@vue/devtools-shared": "^7.7.6", "execa": "^9.5.2", "sirv": "^3.0.1", "vite-plugin-inspect": "0.8.9", "vite-plugin-vue-inspector": "^5.3.1"}, "engines": {"node": ">=v14.21.3"}, "peerDependencies": {"vite": "^3.1.0 || ^4.0.0-0 || ^5.0.0-0 || ^6.0.0-0"}}, "node_modules/vite-plugin-vue-devtools/node_modules/execa": {"version": "9.6.0", "dev": true, "license": "MIT", "dependencies": {"@sindresorhus/merge-streams": "^4.0.0", "cross-spawn": "^7.0.6", "figures": "^6.1.0", "get-stream": "^9.0.0", "human-signals": "^8.0.1", "is-plain-obj": "^4.1.0", "is-stream": "^4.0.1", "npm-run-path": "^6.0.0", "pretty-ms": "^9.2.0", "signal-exit": "^4.1.0", "strip-final-newline": "^4.0.0", "yoctocolors": "^2.1.1"}, "engines": {"node": "^18.19.0 || >=20.5.0"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/vite-plugin-vue-devtools/node_modules/figures": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"is-unicode-supported": "^2.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/vite-plugin-vue-devtools/node_modules/get-stream": {"version": "9.0.1", "dev": true, "license": "MIT", "dependencies": {"@sec-ant/readable-stream": "^0.4.1", "is-stream": "^4.0.1"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/vite-plugin-vue-devtools/node_modules/human-signals": {"version": "8.0.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18.0"}}, "node_modules/vite-plugin-vue-devtools/node_modules/is-stream": {"version": "4.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/vite-plugin-vue-devtools/node_modules/is-unicode-supported": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/vite-plugin-vue-devtools/node_modules/npm-run-path": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"path-key": "^4.0.0", "unicorn-magic": "^0.3.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/vite-plugin-vue-devtools/node_modules/path-key": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/vite-plugin-vue-devtools/node_modules/signal-exit": {"version": "4.1.0", "dev": true, "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/vite-plugin-vue-devtools/node_modules/strip-final-newline": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/vite-plugin-vue-inspector": {"version": "5.3.1", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.23.0", "@babel/plugin-proposal-decorators": "^7.23.0", "@babel/plugin-syntax-import-attributes": "^7.22.5", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-typescript": "^7.22.15", "@vue/babel-plugin-jsx": "^1.1.5", "@vue/compiler-dom": "^3.3.4", "kolorist": "^1.8.0", "magic-string": "^0.30.4"}, "peerDependencies": {"vite": "^3.0.0-0 || ^4.0.0-0 || ^5.0.0-0 || ^6.0.0-0"}}, "node_modules/vite/node_modules/fdir": {"version": "6.4.5", "dev": true, "license": "MIT", "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/vite/node_modules/picomatch": {"version": "4.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/vitest": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "^5.2.2", "@vitest/expect": "3.2.0", "@vitest/mocker": "3.2.0", "@vitest/pretty-format": "^3.2.0", "@vitest/runner": "3.2.0", "@vitest/snapshot": "3.2.0", "@vitest/spy": "3.2.0", "@vitest/utils": "3.2.0", "chai": "^5.2.0", "debug": "^4.4.1", "expect-type": "^1.2.1", "magic-string": "^0.30.17", "pathe": "^2.0.3", "picomatch": "^4.0.2", "std-env": "^3.9.0", "tinybench": "^2.9.0", "tinyexec": "^0.3.2", "tinyglobby": "^0.2.14", "tinypool": "^1.1.0", "tinyrainbow": "^2.0.0", "vite": "^5.0.0 || ^6.0.0 || ^7.0.0-0", "vite-node": "3.2.0", "why-is-node-running": "^2.3.0"}, "bin": {"vitest": "vitest.mjs"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}, "peerDependencies": {"@edge-runtime/vm": "*", "@types/debug": "^4.1.12", "@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0", "@vitest/browser": "3.2.0", "@vitest/ui": "3.2.0", "happy-dom": "*", "jsdom": "*"}, "peerDependenciesMeta": {"@edge-runtime/vm": {"optional": true}, "@types/debug": {"optional": true}, "@types/node": {"optional": true}, "@vitest/browser": {"optional": true}, "@vitest/ui": {"optional": true}, "happy-dom": {"optional": true}, "jsdom": {"optional": true}}}, "node_modules/vitest/node_modules/picomatch": {"version": "4.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/void-elements": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/void-elements/-/void-elements-3.1.0.tgz", "integrity": "sha1-YU9/v42AHwu18GYfWy9XhXUOTwk=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/vscode-uri": {"version": "3.1.0", "dev": true, "license": "MIT"}, "node_modules/vue": {"version": "3.5.18", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue/-/vue-3.5.18.tgz", "integrity": "sha1-PWIkJa0TkaKwE4MjIR7HhPRBVoY=", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.18", "@vue/compiler-sfc": "3.5.18", "@vue/runtime-dom": "3.5.18", "@vue/server-renderer": "3.5.18", "@vue/shared": "3.5.18"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/vue-component-type-helpers": {"version": "2.2.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-component-type-helpers/-/vue-component-type-helpers-2.2.10.tgz", "integrity": "sha1-Emp9clj3RYxm3+I0phJQDJDEg4w=", "license": "MIT"}, "node_modules/vue-docgen-api": {"version": "4.79.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-docgen-api/-/vue-docgen-api-4.79.2.tgz", "integrity": "sha1-3ixJlgFHLzhdwoAGdC4iCLqScwY=", "license": "MIT", "dependencies": {"@babel/parser": "^7.24.7", "@babel/types": "^7.24.7", "@vue/compiler-dom": "^3.2.0", "@vue/compiler-sfc": "^3.2.0", "ast-types": "^0.16.1", "esm-resolve": "^1.0.8", "hash-sum": "^2.0.0", "lru-cache": "^8.0.3", "pug": "^3.0.2", "recast": "^0.23.1", "ts-map": "^1.0.3", "vue-inbrowser-compiler-independent-utils": "^4.69.0"}, "peerDependencies": {"vue": ">=2"}}, "node_modules/vue-docgen-api/node_modules/lru-cache": {"version": "8.0.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lru-cache/-/lru-cache-8.0.5.tgz", "integrity": "sha1-mD/jN/PhdmZ/jlZ8/M58sGTqIU4=", "license": "ISC", "engines": {"node": ">=16.14"}}, "node_modules/vue-eslint-parser": {"version": "10.1.3", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.4.0", "eslint-scope": "^8.2.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.6.0", "lodash": "^4.17.21", "semver": "^7.6.3"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://github.com/sponsors/mysticatea"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0"}}, "node_modules/vue-eslint-parser/node_modules/eslint-visitor-keys": {"version": "4.2.0", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/vue-i18n": {"version": "11.1.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-i18n/-/vue-i18n-11.1.5.tgz", "integrity": "sha1-NOi81N9wnkiLD/EtUA/wvbdb7A0=", "license": "MIT", "dependencies": {"@intlify/core-base": "11.1.5", "@intlify/shared": "11.1.5", "@vue/devtools-api": "^6.5.0"}, "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}, "peerDependencies": {"vue": "^3.0.0"}}, "node_modules/vue-i18n/node_modules/@intlify/shared": {"version": "11.1.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/shared/-/shared-11.1.5.tgz", "integrity": "sha1-I26wsrAYdve1QN20B54gJ8/IPWM=", "license": "MIT", "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}}, "node_modules/vue-i18n/node_modules/@vue/devtools-api": {"version": "6.6.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/devtools-api/-/devtools-api-6.6.4.tgz", "integrity": "sha1-y+l/4BYrNl7cHbqA4XP5BJJTU0M=", "license": "MIT"}, "node_modules/vue-inbrowser-compiler-independent-utils": {"version": "4.71.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-inbrowser-compiler-independent-utils/-/vue-inbrowser-compiler-independent-utils-4.71.1.tgz", "integrity": "sha1-3GgwsgT3z9ww/8TzG6gbDHLFITY=", "license": "MIT", "peerDependencies": {"vue": ">=2"}}, "node_modules/vue-router": {"version": "4.5.1", "license": "MIT", "dependencies": {"@vue/devtools-api": "^6.6.4"}, "funding": {"url": "https://github.com/sponsors/posva"}, "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/vue-router/node_modules/@vue/devtools-api": {"version": "6.6.4", "license": "MIT"}, "node_modules/vue-toast-notification": {"version": "3.1.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-toast-notification/-/vue-toast-notification-3.1.3.tgz", "integrity": "sha1-za7xHL5ARfPqnT6lyBT6AzNhDdE=", "license": "MIT", "engines": {"node": ">=12.15.0"}, "peerDependencies": {"vue": "^3.0"}}, "node_modules/vue-tsc": {"version": "2.2.10", "dev": true, "license": "MIT", "dependencies": {"@volar/typescript": "~2.4.11", "@vue/language-core": "2.2.10"}, "bin": {"vue-tsc": "bin/vue-tsc.js"}, "peerDependencies": {"typescript": ">=5.0.0"}}, "node_modules/w3c-xmlserializer": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"xml-name-validator": "^5.0.0"}, "engines": {"node": ">=18"}}, "node_modules/w3c-xmlserializer/node_modules/xml-name-validator": {"version": "5.0.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18"}}, "node_modules/wait-on": {"version": "8.0.3", "dev": true, "license": "MIT", "dependencies": {"axios": "^1.8.2", "joi": "^17.13.3", "lodash": "^4.17.21", "minimist": "^1.2.8", "rxjs": "^7.8.2"}, "bin": {"wait-on": "bin/wait-on"}, "engines": {"node": ">=12.0.0"}}, "node_modules/watchpack": {"version": "2.4.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/watchpack/-/watchpack-2.4.4.tgz", "integrity": "sha1-RzvacvCFBFPaZCUIHqRvwNdgKUc=", "license": "MIT", "dependencies": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}, "engines": {"node": ">=10.13.0"}}, "node_modules/webidl-conversions": {"version": "7.0.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}}, "node_modules/webpack-virtual-modules": {"version": "0.6.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/webpack-virtual-modules/-/webpack-virtual-modules-0.6.2.tgz", "integrity": "sha1-BX+qkGXIrPSPJMtXrA53c5q5p+g=", "license": "MIT"}, "node_modules/whatwg-encoding": {"version": "3.1.1", "dev": true, "license": "MIT", "dependencies": {"iconv-lite": "0.6.3"}, "engines": {"node": ">=18"}}, "node_modules/whatwg-mimetype": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/whatwg-url": {"version": "14.2.0", "dev": true, "license": "MIT", "dependencies": {"tr46": "^5.1.0", "webidl-conversions": "^7.0.0"}, "engines": {"node": ">=18"}}, "node_modules/which": {"version": "2.0.2", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/which-typed-array": {"version": "1.1.19", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/which-typed-array/-/which-typed-array-1.1.19.tgz", "integrity": "sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/why-is-node-running": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"siginfo": "^2.0.0", "stackback": "0.0.2"}, "bin": {"why-is-node-running": "cli.js"}, "engines": {"node": ">=8"}}, "node_modules/with": {"version": "7.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/with/-/with-7.0.2.tgz", "integrity": "sha1-zO461ULSVTinp6gKrSErmChJW6w=", "license": "MIT", "dependencies": {"@babel/parser": "^7.9.6", "@babel/types": "^7.9.6", "assert-never": "^1.2.1", "babel-walk": "3.0.0-canary-5"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/word-wrap": {"version": "1.2.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wordwrap": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/wordwrap/-/wordwrap-1.0.0.tgz", "integrity": "sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=", "license": "MIT"}, "node_modules/wrap-ansi": {"version": "8.1.0", "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/ansi-regex": {"version": "6.1.0", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/ansi-styles": {"version": "6.2.1", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/emoji-regex": {"version": "9.2.2", "license": "MIT"}, "node_modules/wrap-ansi/node_modules/string-width": {"version": "5.1.2", "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/wrap-ansi/node_modules/strip-ansi": {"version": "7.1.0", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "dev": true, "license": "ISC"}, "node_modules/ws": {"version": "8.18.2", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xml-name-validator": {"version": "4.0.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12"}}, "node_modules/xml2js": {"version": "0.5.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/xml2js/-/xml2js-0.5.0.tgz", "integrity": "sha1-2UQGMfuy7YACA/rRBvJyT2LEk7c=", "license": "MIT", "dependencies": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/xmlbuilder": {"version": "11.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/xmlbuilder/-/xmlbuilder-11.0.1.tgz", "integrity": "sha1-vpuuHIoEbnazESdyY0fQrXACvrM=", "license": "MIT", "engines": {"node": ">=4.0"}}, "node_modules/xmlchars": {"version": "2.2.0", "dev": true, "license": "MIT"}, "node_modules/yallist": {"version": "3.1.1", "dev": true, "license": "ISC"}, "node_modules/yaml": {"version": "2.8.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/yaml/-/yaml-2.8.0.tgz", "integrity": "sha1-FfjJhmIRvcLTeBoIkORNT6Gl//Y=", "dev": true, "license": "ISC", "optional": true, "peer": true, "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14.6"}}, "node_modules/yaml-eslint-parser": {"version": "0.3.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/yaml-eslint-parser/-/yaml-eslint-parser-0.3.2.tgz", "integrity": "sha1-x/XzkE8cBq1V3HExpzGwGEJrSJg=", "license": "MIT", "dependencies": {"eslint-visitor-keys": "^1.3.0", "lodash": "^4.17.20", "yaml": "^1.10.0"}}, "node_modules/yaml-eslint-parser/node_modules/eslint-visitor-keys": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=", "license": "Apache-2.0", "engines": {"node": ">=4"}}, "node_modules/yaml-eslint-parser/node_modules/yaml": {"version": "1.10.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/yaml/-/yaml-1.10.2.tgz", "integrity": "sha1-IwHF/78StGfejaIzOkWeKeeSDks=", "license": "ISC", "engines": {"node": ">= 6"}}, "node_modules/yauzl": {"version": "2.10.0", "dev": true, "license": "MIT", "dependencies": {"buffer-crc32": "~0.2.3", "fd-slicer": "~1.1.0"}}, "node_modules/yocto-queue": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/yoctocolors": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}}}