import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import tailwindcss from '@tailwindcss/vite'
import { resolve, dirname } from 'node:path'
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite'

// https://vite.dev/config/
/* export default defineConfig({
  plugins: [vue(),
    vueDevTools(),
    tailwindcss(),
    VueI18nPlugin({
        runtimeOnly: false,
        include: resolve(dirname(fileURLToPath(import.meta.url)), './src/i18n/locales/**')
      })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
      '~': fileURLToPath(new URL('./node_modules', import.meta.url))
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Separar node_modules per vendor
          if (id.includes('node_modules')) {
            // Vue ecosystem
            if (id.includes('vue') || id.includes('vue-router') || id.includes('vue-i18n')) {
              return 'vue-vendor'
            }

            // ag-grid (molt gran)
            if (id.includes('ag-grid')) {
              return 'ag-grid'
            }

            // FontAwesome
            if (id.includes('@fortawesome')) {
              return 'fontawesome'
            }

            // Pinia
            if (id.includes('pinia')) {
              return 'stores'
            }

            // Axios
            if (id.includes('axios')) {
              return 'network'
            }

            // Parlem components
            if (id.includes('parlem-webcomponents-common')) {
              return 'parlem-components'
            }

            // Altres vendors
            return 'vendor'
          }

          // Separar per mòduls de l'aplicació
          if (id.includes('src/modules/')) {
            if (id.includes('modules/applications')) return 'module-applications'
            if (id.includes('modules/roles')) return 'module-roles'
            if (id.includes('modules/functions')) return 'module-functions'
            if (id.includes('modules/users')) return 'module-users'
            if (id.includes('modules/profiles')) return 'module-profiles'
            if (id.includes('modules/dealers')) return 'module-dealers'
            if (id.includes('modules/subdealers')) return 'module-subdealers'
          }

          // Separar stores
          if (id.includes('src/stores/')) {
            return 'app-stores'
          }

          // Separar components comuns
          if (id.includes('src/components/')) {
            if (id.includes('components/list')) return 'list-components'
            return 'common-components'
          }

          // Separar utils i helpers
          if (id.includes('src/utils/') || id.includes('src/helpers/')) {
            return 'utils'
          }
        }
      }
    },
    // Reduir el límit d'advertència de chunks
    chunkSizeWarningLimit: 500,
    // Usar esbuild per minificació (més ràpid que terser)
    minify: 'esbuild'
  }
  // Configuració d'esbuild
    esbuild: {
    drop: ['console', 'debugger'] // Elimina console.logs i debuggers en producció
  }


})
 */

// https://vite.dev/config/
export default ({ mode }: any) => {
  process.env = {
    ...process.env,
    ...loadEnv(mode, process.cwd())
  }
  return defineConfig({
    mode: JSON.stringify(process.env.VITE_NODE_ENV),
    plugins: [
      vue(),
      vueDevTools(),
      tailwindcss(),
      VueI18nPlugin({
        runtimeOnly: false,
        include: resolve(dirname(fileURLToPath(import.meta.url)), './src/i18n/locales/**')
      })
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '~': fileURLToPath(new URL('./node_modules', import.meta.url))
      }
    },
    build: {
      chunkSizeWarningLimit: 1600,
      rollupOptions: {
        output: {
          manualChunks(id: any) {
            if (id.includes('node_modules')) {
              return id.toString().split('node_modules/')[1].split('/')[0].toString()
            }
          }
        }
      }
    },
    define: {
      'process.env.NODE_ENV': JSON.stringify(process.env.VITE_NODE_ENV)
    }
  })
}
