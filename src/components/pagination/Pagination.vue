<template>
  <div
    class="absolute bottom-0 w-full bg-background-color z-20 border-t-2 border-gray flex justify-between pt-2"
  >
    <p class="text-dark dark:text-white">A la llista hi ha {{ searchItemsLength }} elements</p>
    <div class="flex">
      <PwTabs :tabs="pageSizeTabs" @tab-click="emit('changePageSize', $event)" class="flex mr-2" />
      <PwTabs v-if="isLoading" :tabs="isLoadingTab" class="flex" />
      <PwTabs
        v-if="showMoreItemsTab"
        :tabs="moreItemsTab"
        @tab-click="emit('getMoreItems')"
        class="flex"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { PwTabs } from 'parlem-webcomponents-common'
import { useI18n } from 'vue-i18n'
import type { ITab } from './interfaces/tab.interface'

const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false
  },
  searchItemsLength: {
    type: Number,
    default: 0
  },
  showMoreItemsTab: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits<{
  getMoreItems: []
  changePageSize: [value: ITab]
}>()

const { t } = useI18n()

const pageSizeTabs: ITab[] = [
  { label: '50', disabled: false, active: true },
  { label: '100', disabled: false, active: false },
  { label: '200', disabled: false, active: false }
]

const moreItemsTab: ITab[] = [
  {
    label: t('pagination.show-more'),
    disabled: false,
    active: true,
    icon: 'fa-plus'
  }
]
const isLoadingTab: ITab[] = [
  {
    label: t('pagination.show-more'),
    disabled: true,
    active: false,
    icon: 'fa-spinner'
  }
]
</script>

<style></style>
