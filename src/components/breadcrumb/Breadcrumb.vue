<template>
  <PwBreadcrumb class="mb-3" :icon="icon" :breadcrumbs="breadcrumbs"> </PwBreadcrumb>
</template>

<script lang="ts">
export default {
  name: 'BreadcrumbComponent'
}
</script>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { PwBreadcrumb } from 'parlem-webcomponents-common'
import type { IBreadcrumbTab } from './interfaces/breadcrumb.interface'
import { useRoute } from 'vue-router'
import type { RouteLocationNormalizedLoaded, RouteRecordNormalized } from 'vue-router'

const route: RouteLocationNormalizedLoaded = useRoute()

const breadcrumbs = ref<IBreadcrumbTab[]>([])

const icon = computed(() => {
  const defaultIcon = 'fa-home'
  const routeIcon = route.meta && route.meta.icon
  return typeof routeIcon === 'string' ? routeIcon : defaultIcon
})

const generateBreadcrumbs = () => {
  const matchedRoutes = route.matched
  breadcrumbs.value = []

  matchedRoutes.forEach((selectedRoute: RouteRecordNormalized) => {
    if (selectedRoute.meta?.breadcrumb) {
      const title =
        typeof selectedRoute.meta.breadcrumb.title === 'function'
          ? selectedRoute.meta.breadcrumb.title(route)
          : selectedRoute.meta.breadcrumb.title

      let resolvedPath = selectedRoute.path
      Object.entries(route.params).forEach(([key, val]) => {
        resolvedPath = resolvedPath.replace(`:${key}`, String(val))
      })

      breadcrumbs.value.push({
        name: title,
        route: resolvedPath
      })
    }
  })
}

watch(route, generateBreadcrumbs, { immediate: true })
</script>

<style></style>
