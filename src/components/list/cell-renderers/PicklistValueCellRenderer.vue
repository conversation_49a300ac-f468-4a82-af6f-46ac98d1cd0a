<template>
  <p>{{ valueToShow || '-' }}</p>
</template>

<script lang="ts">
import type { ICellRendererParams } from 'ag-grid-community'
import { GET_PICKLISTS } from '@/stores/hierarchy/constants/hierarchy.getters.constants'
import { useHierarchyStore } from '@/stores/hierarchy'
import type { IHierarchyStore } from '@/stores/hierarchy/interfaces/store.interface'
import type { IPicklist } from '@/services/api/hierarchy/interfaces'
import { computed, type ComputedRef } from 'vue'

export default {
  props: {
    params: Object as () => ICellRendererParams
  },
  setup(props) {
    const itemValue = props.params?.value
    const picklistName = props.params?.colDef?.cellRendererParams?.picklistName
    const hierarchyStore: IHierarchyStore = useHierarchyStore()
    const picklists: ComputedRef<IPicklist[]> = computed(() => hierarchyStore[`${GET_PICKLISTS}`])
    const selectedPicklist = picklists.value.find((picklist) => picklist.name === picklistName)

    const mapPicklistValue = (items: string[]) => {
      return items
        .map((item: string) => {
          const picklistValue = selectedPicklist?.values.find((value) => value.value === item)
          return picklistValue?.label
        })
        .join(', ')
    }

    const findPicklistValue = (item: string) => {
      const picklistValue = selectedPicklist?.values.find((value) => value.value === item)
      return picklistValue?.label
    }

    const valueToShow = Array.isArray(itemValue)
      ? mapPicklistValue(itemValue)
      : findPicklistValue(itemValue)

    return {
      valueToShow
    }
  }
}
</script>
