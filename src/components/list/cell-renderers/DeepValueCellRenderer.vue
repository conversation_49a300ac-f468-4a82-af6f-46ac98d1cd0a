<template>
  <p>{{ deepValue }}</p>
</template>

<script lang="ts">
import { ref, computed } from 'vue'
import type { ComputedRef, Ref } from 'vue'
import { GET_SEARCH_ITEMS } from '@/stores/hierarchy/constants/hierarchy.getters.constants'
import { useHierarchyStore } from '@/stores/hierarchy'
import type { IHierarchyStore } from '@/stores/hierarchy/interfaces/store.interface'
import getDeepValue from '@/helpers/get-deep-value/getDeepValue'
import type { ICellRendererParams } from 'ag-grid-community'

export default {
  props: {
    params: Object as () => ICellRendererParams
  },
  setup(props) {
    const itemValue = props.params?.data
    const field = props.params?.colDef?.field
    const hierarchyItemKey = itemValue.hierarchyItemKey
    const hierarchyStore: IHierarchyStore = useHierarchyStore()

    const originalItems: ComputedRef<any[]> = computed(
      () => hierarchyStore[`${GET_SEARCH_ITEMS}`][`${hierarchyItemKey}`]
    )

    const originalItem: Ref<any> = ref(
      originalItems.value?.find((item) => item.id === itemValue.id)
    )

    const deepValue: Ref<string | undefined> = ref(
      getDeepValue(originalItem.value, field as string)
    )

    return {
      deepValue
    }
  }
}
</script>

<style></style>
