<template>
  <div class="flex items-center h-full">
    <font-awesome-icon
      v-for="(action, index) of actions"
      :key="index"
      :icon="`fa-solid fa-${action.icon}`"
      class="max-w-[14px]"
      :class="`
            ${index > 0 ? 'ml-2.5' : ''}
            ${
              iconIsDisabled(action.key)
                ? 'text-gray-light dark:text-dark-gray-light'
                : 'text-gray dark:text-white cursor-pointer hover:text-primary dark:hover:text-primary'
            }
          `"
      @click="iconIsDisabled(action.key) ? '' : submitAction(action.key)"
      :title="getTooltip(action.key)"
    />
  </div>
</template>

<script lang="ts">
import { useRouter } from 'vue-router'
import { useHierarchyStore } from '@/stores/hierarchy'
import type { IHierarchyStore } from '@/stores/hierarchy/interfaces/store.interface'
import type { ICellRendererParams } from 'ag-grid-community'

export default {
  props: {
    params: Object as () => ICellRendererParams
  },
  setup(props) {
    const router = useRouter()
    const hierarchyStore: IHierarchyStore = useHierarchyStore()
    const itemValue = props.params?.data
    const actions = [
      { icon: 'circle-info', key: 'detail' },
      { icon: 'pencil', key: 'edit' },
      { icon: 'trash-can', key: 'delete' }
    ]

    const canDelete =
      itemValue.deactivationDate === '-' ||
      (itemValue.endDate && new Date(itemValue.endDate).toISOString() > new Date().toISOString())

    /*    const canEdit =
      ((itemValue.endDate === '-' ||
        (itemValue.endDate !== '-' && new Date() < new Date(itemValue.endDate))) &&
        !itemValue.isProductive) ||
      (itemValue.itemType === 'exports' && !itemValue.exportedProd) ||
      (itemValue.itemType === 'catalogs' && !itemValue.isActive) ||
      itemValue.itemType === 'groupRates' */

    function iconIsDisabled(actionKey: string) {
      if (actionKey === 'delete') return !canDelete
    }

    function getTooltip(actionKey: string) {
      switch (actionKey) {
        case 'edit':
          return 'Editar'
        case 'delete':
          return 'Eliminar'
        case 'clone':
          return 'Clonar'
        case 'detail':
          return 'Veure detalls'
        case 'change':
          return 'Canviar estat'
        case 'add':
          return 'Afegir opcionals'
        default:
          return ''
      }
    }

    function goTo(actionKey: string) {
      /*   if (isChild && ['edit', 'add'].includes(actionKey)) {
        const routeData = router.resolve({
          name: `${itemValue.itemType}-${actionKey}-step1`,
          params: {
            id: itemValue.id
          }
        })
        return window.open(routeData.href, '_blank')
      } */
      router.push({
        name: `${itemValue.itemType}-${actionKey}-step1`,
        params: {
          id: itemValue.id
        }
      })
    }

    function submitAction(actionKey: string) {
      if (['edit'].includes(actionKey)) {
        goTo(actionKey)
        return
      }
      if (actionKey === 'detail') {
        const entityType = itemValue.itemType || router.currentRoute.value.path.split('/')[1]

        // Redirigir a la vista de detalle
        router.push({
          name: `${entityType}-detail`, // 'roles-detail'
          params: {
            id: itemValue.id
          }
        })
        return
      }
      if (actionKey === 'delete') {
        const item = { type: actionKey, item: itemValue }
        hierarchyStore.setPopUpItem(item)
      }
    }

    return {
      actions,
      iconIsDisabled,
      submitAction,
      getTooltip
    }
  }
}
</script>
