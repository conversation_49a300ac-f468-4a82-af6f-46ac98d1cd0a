<template>
  <div class="flex h-full items-center justify-center">
    <div
      class="h-[26px] min-w-[85px] w-[85px] rounded-full flex items-center justify-center"
      :class="setBadgeColor()"
    >
      <p class="text-center font-bold">{{ statusValue }}</p>
    </div>
  </div>
</template>

<script lang="ts">
import { ref, computed } from 'vue'
import type { ComputedRef, Ref } from 'vue'
import type { ICellRendererParams } from 'ag-grid-community'

export default {
  props: {
    params: Object as () => ICellRendererParams
  },
  setup(props) {
    const itemValue = props.params?.value
    const statusValue: Ref<string> = ref('')
    const isNotActive: ComputedRef<boolean> = computed(
      () =>
        itemValue &&
        itemValue !== '-' &&
        Date.parse(new Date(itemValue).toString()) &&
        Date.parse(new Date(itemValue).toString()) <= Date.parse(new Date().toString())
    )

    function setStatusValue() {
      statusValue.value = isNotActive.value ? 'Desactivat' : 'Activat'
    }
    setStatusValue()

    function setBadgeColor() {
      return isNotActive.value ? 'bg-error-light text-error' : 'bg-success-light text-success'
    }

    return { statusValue, setBadgeColor }
  }
}
</script>

<style></style>
