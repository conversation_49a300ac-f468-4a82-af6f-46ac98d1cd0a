<template>
  <div
    v-for="(child, index) in selectedChildrenOptions.children"
    :key="index"
    class="h-full -mt-[1px]"
    v-show="parentItem[child]?.length"
  >
    <div
      class="w-full flex items-center bg-primary-light px-[15px] h-[24px] max-h-[24px] border-b border-primary"
    >
      <h3 class="font-bold text-sm text-primary">
        {{ t(`item-type.${child}`) }} {{ parentItem.name }}
      </h3>
    </div>
    <hr class="text-primary" />
    <List
      :hierarchyItemType="itemData.hierarchyItemType"
      :hierarchyItemKey="itemData.hierarchyItemKey"
      :itemsToShow="parentItem[child]"
      :columns="getColumnFields(selectedChildrenOptions.childrenColumn[child])"
      :isChild="true"
    ></List>
    <hr class="text-gray absolute w-full bottom-[3px]" />
    <hr class="text-gray absolute w-full bottom-0" />
  </div>
</template>

<script lang="ts">
export default {
  name: 'FullGrid'
}
</script>

<script setup lang="ts">
import List from '@/components/list/List.vue'
import { useI18n } from 'vue-i18n'
import childrenOptionsConfig from '@/schema/list-children-config/childrenOptionsConfig.index'
import getColumnFields from '@/schema/list-columns-config/getColumnFields'
import type { ICellRendererParams } from 'ag-grid-community'

const props = defineProps({
  params: {
    type: Object as () => ICellRendererParams,
    required: true
  }
})
const { t } = useI18n()

const parentItem = props.params.data.parentItem
const itemData = props.params.data
const selectedChildrenOptions = (childrenOptionsConfig as any)[itemData.hierarchyItemKey]
</script>
<style></style>
