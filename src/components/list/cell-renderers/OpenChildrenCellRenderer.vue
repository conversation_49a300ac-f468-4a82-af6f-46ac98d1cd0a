<template>
  <div v-if="showArrowIcon">
    <font-awesome-icon
      v-if="openDetails.includes(selectedItemId)"
      @click="deleteChildrenFromOpenList()"
      icon="fa-solid fa-chevron-up"
      class="max-w-[14px] text-gray dark:text-gray cursor-pointer hover:text-primary dark:hover:text-primary"
    ></font-awesome-icon>
    <font-awesome-icon
      v-else
      @click="addChildrenToOpenList()"
      icon="fa-solid fa-chevron-down"
      class="max-w-[14px] text-gray dark:text-gray cursor-pointer hover:text-primary dark:hover:text-primary"
    ></font-awesome-icon>
  </div>
</template>

<script lang="ts">
import { useHierarchyStore } from '@/stores/hierarchy'
import {
  GET_SEARCH_ITEMS,
  GET_OPEN_LIST_CHILDREN
} from '@/stores/hierarchy/constants/hierarchy.getters.constants'
import type { IHierarchyStore } from '@/stores/hierarchy/interfaces/store.interface'
import { useWorkflowStore } from '@/stores/workflow'
import type { IWorkflowStore } from '@/stores/workflow/interfaces/store.interface'
import { computed, type ComputedRef } from 'vue'
import type { ICellRendererParams } from 'ag-grid-community'

export default {
  props: {
    params: Object as () => ICellRendererParams
  },
  setup(props) {
    const hierarchyStore: IHierarchyStore = useHierarchyStore()
    const workflowStore: IWorkflowStore = useWorkflowStore()
    const selectedItemId = props.params?.data.id
    const selectedItemKey = props.params?.data.hierarchyItemKey
    const originalItems: ComputedRef<any[]> = computed(
      () => hierarchyStore[`${GET_SEARCH_ITEMS}`][`${selectedItemKey}`]
    )
    const selectedItem: ComputedRef<unknown> = computed(() => {
      const item = originalItems.value?.find((item: { id: string }) => item.id === selectedItemId)
      return item
    })
    const selectedItemIndex: ComputedRef<number> = computed(() => {
      const item = originalItems.value?.findIndex(
        (item: { id: string }) => item.id === selectedItemId
      )
      return item
    })
    const openDetails: ComputedRef<string[]> = computed(
      () => workflowStore[`${GET_OPEN_LIST_CHILDREN}`]?.[`${selectedItemKey}`] || []
    )

    const cellRendererParams = props.params?.colDef?.cellRendererParams

    const childrenProperties = Array.isArray(cellRendererParams.childrenType)
      ? cellRendererParams.childrenType
      : [cellRendererParams.childrenType]

    const showArrowIcon = childrenProperties.some((property: string) => {
      const childrenArray = (selectedItem.value as Record<string, unknown>)?.[property]
      return Array.isArray(childrenArray) && childrenArray.length > 0
    })

    function addChildrenToOpenList() {
      workflowStore.addChildrenToOpenList(selectedItemKey, selectedItemId)
      hierarchyStore.addRowChildren(
        selectedItemKey,
        selectedItemIndex.value,
        selectedItem.value as { id: string | number }
      )
    }

    function deleteChildrenFromOpenList() {
      workflowStore.deleteChildrenFromOpenList(selectedItemKey, selectedItemId)
      hierarchyStore.removeRowChildren(
        selectedItemKey,
        selectedItem.value as { id: string | number }
      )
    }

    return {
      showArrowIcon,
      addChildrenToOpenList,
      deleteChildrenFromOpenList,
      selectedItemId,
      selectedItem,
      openDetails
    }
  }
}
</script>

<style></style>
