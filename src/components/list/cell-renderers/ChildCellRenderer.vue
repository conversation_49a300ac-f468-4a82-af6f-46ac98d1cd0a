<template>
  <div class="flex justify-between -ml-0.5 items-center">
    <div class="flex items-center">
      <div class="relative">
        <div
          class="absolute w-[1px] top-0 left-[0.595rem] bg-gray-light dark:bg-dark-gray-light h-1/6"
        ></div>
        <font-awesome-icon
          icon="fa-regular fa-circle-dot"
          class="w-[20px] text-gray-light dark:text-dark-gray-light z-10"
        />
        <div
          class="absolute w-[1px] bottom-[2px] left-[0.595rem] bg-gray-light dark:bg-dark-gray-light h-1/6"
        ></div>
      </div>
      <div class="h-[1px] bg-gray-light dark:bg-dark-gray-light w-1.5 z-20 -ml-1"></div>
    </div>
  </div>
</template>

<script lang="ts">
import { ref } from 'vue'
import type { Ref } from 'vue'
import type { ICellRendererParams } from 'ag-grid-community'

export default {
  props: {
    params: Object as () => ICellRendererParams
  },
  setup(props) {
    const id: Ref<number> = ref(props.params?.value)
    return {
      id
    }
  }
}
</script>

<style></style>
