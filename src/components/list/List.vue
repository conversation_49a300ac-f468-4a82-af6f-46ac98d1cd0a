<template>
  <ag-grid-vue
    v-if="rowData?.length"
    :columnDefs="columnDefs"
    :rowData="rowData"
    :autoSizeStrategy="autoSizeStrategy"
    :suppressCellSelection="false"
    :defaultColDef="defaultColDef"
    :headerHeight="headerHeight"
    rowSelection="multiple"
    :isFullWidthRow="isFullWidthRow"
    :fullWidthCellRenderer="fullWidthCellRenderer"
    :getRowHeight="getRowHeight"
    :enableCellTextSelection="true"
    :theme="theme"
    @grid-ready="onGridReady"
    :domLayout="isChild ? 'autoHeight' : undefined"
    :style="{
      '--ag-header-background-color': isChild
        ? isDark
          ? '#2D3033'
          : '#f1f1f1'
        : isDark
          ? '#3c4043'
          : '#e4e4e4'
    }"
  >
  </ag-grid-vue>
</template>

<script lang="ts">
export default {
  name: 'SubscriptionStateList'
}
</script>

<script setup lang="ts">
import { ref, onBeforeMount, watch } from 'vue'
import type { Ref, PropType } from 'vue'
import { AgGridVue } from 'ag-grid-vue3'
import type { IColumn } from '@/components/list/interfaces/column.interface'

import type { IHierarchyType } from '@/stores/hierarchy/interfaces/hierarchy-type.interface'
import {
  AllCommunityModule,
  ModuleRegistry,
  themeQuartz,
  type Theme,
  type GridApi,
  type SizeColumnsToFitGridStrategy
} from 'ag-grid-community'
import getCssVariable from '@/helpers/get-css-variable/getCssVariable'
import FullGrid from '@/components/list/cell-renderers/FullGrid.vue'
import type { GridRowData, ListItem } from '@/components/list/interfaces/grid.interface'
import type { RowHeightParams, IsFullWidthRowParams, GridReadyEvent } from 'ag-grid-community'
// Register all Community features
ModuleRegistry.registerModules([AllCommunityModule])

const props = defineProps({
  hierarchyItemType: {
    type: String as PropType<IHierarchyType>,
    required: true
  },
  hierarchyItemKey: {
    type: String,
    required: true
  },
  itemsToShow: {
    type: Array as PropType<ListItem[]>,
    required: true
  },
  columns: {
    type: Array as PropType<IColumn[]>,
    required: true
  },
  isChild: {
    type: Boolean,
    default: false
  }
})

/* const { locale } = useI18n()
 */ const isDark = localStorage.getItem('theme') === 'dark'
const gridApi = ref<GridApi>()
const autoSizeStrategy = ref<SizeColumnsToFitGridStrategy | undefined>(undefined)
const defaultColDef = ref({
  editable: false,
  filter: true,
  sortable: true,
  wrapText: true,
  autoHeight: true,
  cellStyle: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-start',
    overflow: 'hidden',
    wordBreak: 'normal',
    lineHeight: '1.6',
    height: '100%'
  }
})
const customTheme = themeQuartz.withParams({
  headerBackgroundColor: isDark ? '#3c4043' : '#e4e4e4',
  foregroundColor: isDark
    ? getCssVariable('--vt-c-white-mute')
    : getCssVariable('--vt-c-black-mute'),
  backgroundColor: isDark ? 'transparent' : getCssVariable('--vt-c-white'),
  checkboxCheckedShapeColor: getCssVariable('--vt-c-white'),
  checkboxCheckedBackgroundColor: getCssVariable('--color-primary'),
  rowHoverColor: isDark ? getCssVariable('--vt-c-black-mute') : getCssVariable('--vt-c-white-mute'),

  // Eliminar borders exteriors i fer-los rectes
  borderRadius: 0,
  wrapperBorder: 'none',
  wrapperBorderRadius: 0
})
const isFullWidthRow = ref<((params: IsFullWidthRowParams) => boolean) | undefined>(undefined)
const getRowHeight = ref<((params: RowHeightParams) => number | undefined) | undefined>(undefined)
const fullWidthCellRenderer = ref<typeof FullGrid | undefined>(undefined)
const columnDefs: Ref<IColumn[]> = ref([])
const rowData = ref<GridRowData[]>([])
const theme = ref<Theme | 'legacy'>(customTheme)
const headerHeight = ref<number>(48)
const childrenType = ref<string[] | string | null | undefined>(null)

onBeforeMount(() => {
  renderRows()
})

const getChildrenType = () => {
  if (props.isChild) return null
  const childrenColumn = columnDefs.value.find((col) => col.key.toLowerCase().includes('children'))
  childrenType.value = childrenColumn ? childrenColumn.cellRendererParams?.childrenType : null
}

const renderRows = () => {
  columnDefs.value = props.columns
  autoSizeStrategy.value = {
    type: 'fitGridWidth' /* fitGridWidth, fitProvidedWidth, fitCellContents */
  }
  getChildrenType()
  rowData.value = getRows()
  getRowHeight.value = (params: RowHeightParams): number | undefined => {
    if (isFullWidth(params.data)) {
      const total = (params.data.childrenType as string[])
        .map((type: string) => {
          const len: number = params.data.parentItem[type]?.length ?? 0
          return 34 * len + 34 + 24 + 5
        })
        .reduce((sum: number, val: number) => sum + val, 0)

      return total
    }
    if (props.isChild && props.itemsToShow.length) {
      return 34
    }
    return undefined
  }
  headerHeight.value = props.isChild ? 34 : 48

  isFullWidthRow.value = (params: IsFullWidthRowParams): boolean => {
    return isFullWidth(params.rowNode.data)
  }
  fullWidthCellRenderer.value = FullGrid
}

const onGridReady = (params: GridReadyEvent): void => {
  gridApi.value = params.api
  window.onresize = () => {
    if (gridApi.value) {
      gridApi.value.sizeColumnsToFit()
    }
  }
}

function getRows(): GridRowData[] {
  const itemsRowData: GridRowData[] = []
  props.itemsToShow.forEach((element: ListItem) => {
    const newItem: GridRowData = {
      isChild: element.isChild,
      childrenType: childrenType.value,
      hierarchyItemType: props.hierarchyItemType,
      hierarchyItemKey: props.hierarchyItemKey
    }
    if (newItem.isChild) {
      newItem.parentItem = element.parentItem
    }
    getNewRows(element, newItem)
    itemsRowData.push(newItem)
  })
  return itemsRowData
}

function getNewRows(item: ListItem, newItem: GridRowData): void {
  columnDefs.value.forEach((col: IColumn) => {
    newItem[col.field] =
      item[col.field] === null || item[col.field] === undefined || item[col.field] === ''
        ? '-'
        : item[col.field]
  })
}

function isFullWidth(data: GridRowData): boolean {
  return Boolean(data.isChild)
}

watch(
  () => props.itemsToShow,
  () => {
    renderRows()
  },
  { deep: true }
)

watch(
  () => props.columns,
  () => {
    columnDefs.value = props.columns
  }
)
</script>

<style>
/* Personalització ag-Grid: eliminar borders exteriors i fer-los rectes */
:deep(.ag-theme-quartz) {
  /* Eliminar border exterior del contenidor principal */
  border: none !important;
  border-radius: 0 !important;
}

:deep(.ag-theme-quartz .ag-root-wrapper) {
  /* Eliminar border del wrapper principal */
  border: none !important;
  border-radius: 0 !important;
}

:deep(.ag-theme-quartz .ag-header) {
  /* Eliminar borders del header */
  border-left: none !important;
  border-right: none !important;
  border-top: none !important;
  border-radius: 0 !important;
}

:deep(.ag-theme-quartz .ag-body-viewport) {
  /* Eliminar borders del viewport del body */
  border-left: none !important;
  border-right: none !important;
  border-bottom: none !important;
}

:deep(.ag-theme-quartz .ag-row) {
  /* Mantenir només el border inferior de les files (borders interiors) */
  border-left: none !important;
  border-right: none !important;
  border-top: none !important;
  /* El border-bottom es manté per defecte per separar les files */
}

:deep(.ag-theme-quartz .ag-cell) {
  /* Eliminar borders laterals de les cel·les */
  border-left: none !important;
  border-right: none !important;
  border-radius: 0 !important;
}

:deep(.ag-theme-quartz .ag-header-cell) {
  /* Eliminar borders laterals de les cel·les del header */
  border-left: none !important;
  border-right: none !important;
  border-radius: 0 !important;
}
</style>
