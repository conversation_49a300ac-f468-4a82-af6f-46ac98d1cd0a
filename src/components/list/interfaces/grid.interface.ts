import type { GridApi } from 'ag-grid-community'

// Interfícies per ag-grid
export interface GridReadyParams {
  api: GridApi
}

export interface RowHeightParams {
  data: GridRowData
  node: any
}

export interface IsFullWidthRowParams {
  rowNode: {
    data: GridRowData
  }
}

// Interfície per les dades de les files del grid
export interface GridRowData {
  isChild?: boolean
  childrenType?: string | string[] | null | undefined
  hierarchyItemType?: string
  hierarchyItemKey?: string
  parentItem?: {
    [key: string]: any[]
  }
  [key: string]: any
}

// Interfície per les dades d'entrada
export interface ListItem {
  isChild?: boolean
  parentItem?: {
    functions?: any[]
    [key: string]: any
  }
  [key: string]: any
}

// Tipus per les funcions del grid
export type RowHeightFunction = (params: RowHeightParams) => number | undefined
export type IsFullWidthRowFunction = (params: IsFullWidthRowParams) => boolean
export type GridReadyFunction = (params: GridReadyParams) => void
