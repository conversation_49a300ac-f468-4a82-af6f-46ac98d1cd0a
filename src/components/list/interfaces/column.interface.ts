export interface IColumn {
  field: string
  key: string
  headerName: string
  checkboxSelection?: boolean
  headerCheckboxSelection?: boolean
  minWidth?: number
  maxWidth?: number
  cellRenderer?: any
  editable?: boolean
  filter?: boolean
  sortable?: boolean
  hide?: boolean
  cellStyle?: { [key: string]: string }
  cellRendererParams?: {
    childrenType?: string | string[]
    picklistName?: string
  }
}
