<template>
  <PwPopup v-if="visible" @close="emit('close')" @cancel="emit('cancel')" @accept="emit('accept')">
    <div class="px-4 py-12 max-w-[400px] mx-auto text-center flex flex-col items-center gap-4">
      <font-awesome-icon
        :icon="iconKey ? iconMap[iconKey] : defaultIcon"
        class="w-16 h-16 text-gray-700 dark:text-white my-4"
      />
      <p class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
        {{ message }}
      </p>
    </div>
  </PwPopup>
</template>

<script setup lang="ts">
import { PwPopup } from 'parlem-webcomponents-common'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

const props = defineProps<{
  visible: boolean
  message: string
  iconKey?: 'delete' | 'sync' | 'info' | 'warning' | 'success'
}>()

const emit = defineEmits<{
  (e: 'accept'): void
  (e: 'cancel'): void
  (e: 'close'): void
}>()

const iconMap = {
  delete: ['fas', 'trash'],
  sync: ['fas', 'rotate'],
  info: ['fas', 'circle-info'],
  warning: ['fas', 'triangle-exclamation'],
  success: ['fas', 'circle-check']
}

const defaultIcon = ['far', 'face-smile']
</script>
