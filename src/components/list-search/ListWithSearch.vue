<template>
  <SearchFilter
    :hierarchyItemType="hierarchyItemType"
    :hierarchyItemKey="hierarchyItemKey"
    @filter="filter"
    @is-loading="isLoading = $event"
  ></SearchFilter>
  <div v-if="isLoading" class="mt-20 h-1/2 flex flex-col justify-center items-center">
    <PwLoading
      company="Parlem"
      loading-style="w-40 h-40"
      loading-image-style="w-[24px]"
    ></PwLoading>
    <p class="mt-4 text-lg text-gray animate-pulse">Carregant...</p>
  </div>
  <div v-else>
    <div v-if="searchItems?.length" class="h-full mt-6">
      <List
        class="llista"
        :hierarchyItemType="hierarchyItemType"
        :hierarchyItemKey="hierarchyItemKey"
        :itemsToShow="searchItems"
        :columns="gridColumns"
        :isChild="false"
      ></List>
      <Pagination
        :isLoading="isLoading"
        :searchItemsLength="searchItems.length"
        :showMoreItemsTab="showGetMoreItemsButton()"
        @changePageSize="handleChangePageSize"
        @get-more-items="getMoreItems"
      ></Pagination>
    </div>
    <div v-else class="flex items-center px-12 pt-5 grid grid-cols-5 gap-2 h-3/6">
      <div class="pl-6 col-span-2">
        <p class="text-5xl font-bold text-black dark:text-white">FILTRA PER COMENÇAR</p>
        <p class="text-lg text-dark-gray dark:text-gray mt-2">
          Aplica un filtre per obtenir un llistat de
          {{ $t(`item-type.${props.hierarchyItemType}`)?.toLowerCase() }}
        </p>
      </div>
      <div class="flex justify-center col-span-3">
        <img
          src="/src/assets/svg/web-search-cuate.svg"
          alt="il·lustració d'image de búsqueda."
          class="w-9/12"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import List from '@/components/list/List.vue'
import SearchFilter from '../search/SearchFilter.vue'
import type { ComputedRef, PropType, Ref } from 'vue'
import { computed, ref, onMounted } from 'vue'
import type { ListItem } from '@/components/list/interfaces/grid.interface'
import type { ISearchParams } from '../search/interfaces/search.interface'
import type { ISelectedFilterValue } from '../search/interfaces/selectedFilterValue.interface'
import type { ITab } from '@/components/pagination/interfaces/tab.interface'
import { useHierarchyStore } from '@/stores/hierarchy'
import type { IHierarchyStore } from '@/stores/hierarchy/interfaces/store.interface'
import type { IHierarchyType } from '@/stores/hierarchy/interfaces/hierarchy-type.interface'
import { PwLoading } from 'parlem-webcomponents-common'
import Pagination from '@/components/pagination/Pagination.vue'
import getColumnFields from '@/schema/list-columns-config/getColumnFields'
import columnOptionsConfig from '@/schema/list-columns-config/columnOptionsConfig.index'

const props = defineProps({
  hierarchyItemType: {
    type: String as PropType<IHierarchyType>,
    required: true
  },
  hierarchyItemKey: {
    type: String,
    required: true
  }
})
const hierarchyStore: IHierarchyStore = useHierarchyStore()
const searchItems: ComputedRef<ListItem[]> = computed(
  () => (hierarchyStore.searchItems[`${props.hierarchyItemKey}`] || []) as ListItem[]
)
const gridColumns = computed(() => {
  return getColumnFields(columnOptionsConfig[props.hierarchyItemKey])
})

const isLoading: Ref<boolean> = ref(false)
const searchParams: Ref<ISearchParams> = ref({
  orderBy: 'id',
  orderDirection: 'desc',
  pageNum: 0,
  pageSize: 50
})

onMounted(async () => {
  if (props.hierarchyItemType === 'functions') {
    await filter([])
  }
})

async function filter(filterArray: ISelectedFilterValue[]) {
  isLoading.value = true
  searchParams.value = {
    orderBy: searchParams.value.orderBy,
    orderDirection: searchParams.value.orderDirection,
    pageNum: searchParams.value.pageNum,
    pageSize: searchParams.value.pageSize
  }
  if (filterArray.length > 0) {
    filterArray.forEach(
      (filter: ISelectedFilterValue) =>
        (searchParams.value[filter.firstFilter] = filter.secondFilter)
    )
  } else {
    hierarchyStore.removeSearchItems(props.hierarchyItemKey)
  }
  await getItems()
  isLoading.value = false
}

async function getItems(): Promise<void> {
  isLoading.value = true
  searchParams.value.pageNum = 0

  await hierarchyStore.getItemsBySearch(
    props.hierarchyItemType,
    props.hierarchyItemKey,
    searchParams.value
  )

  isLoading.value = false
}

function handleChangePageSize(tab: ITab): void {
  isLoading.value = true
  const pageSize = parseInt(tab.label)
  searchParams.value.pageSize = pageSize
  getItems()

  isLoading.value = false
}

async function getMoreItems(): Promise<void> {
  isLoading.value = true
  ++searchParams.value.pageNum

  await hierarchyStore.getItemsBySearch(
    props.hierarchyItemType,
    props.hierarchyItemKey,
    searchParams.value
  )

  isLoading.value = false
}

const showGetMoreItemsButton = (): boolean => {
  return searchItems.value.length >= searchParams.value.pageNum + 1 * searchParams.value.pageSize
}
</script>

<style>
.llista {
  height: calc(100vh - 247px - 2.4rem);
}

.llista-cart {
  height: calc(100vh - 177px - 24.5rem);
}
</style>
