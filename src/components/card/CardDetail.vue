<template>
  <div class="card-detail-container mb-2 shadow rounded-md overflow-hidden">
    <!-- Header -->
    <div class="rounded-t-md overflow-hidden flex flex-col">
      <div class="flex justify-between items-center px-4 py-2 dark:bg-dark-gray-light">
        <div class="flex gap-2 items-center">
          <div class="rounded-full size-8 bg-primary-light flex justify-center items-center">
            <font-awesome-icon :icon="`fa fa-${icon}`" class="size-5 text-primary" />
          </div>
          <p class="text-primary dark:text-gray-light font-bold">{{ title }}</p>
        </div>
      </div>
      <hr class="border-t border-gray-300 dark:border-gray-light" />
    </div>

    <!-- Body with image on the left -->
    <div class="rounded-b-md dark:bg-dark-gray-light overflow-visible relative pb-5">
      <div class="grid grid-cols-1 md:grid-cols-5">
        <!-- Imagen lateral -->
        <div v-if="image" class="col-span-1 flex items-start">
          <img
            :src="image"
            alt="Detail image"
            class="object-cover w-full h-full max-w-[200px] rounded-l-md"
          />
        </div>

        <!-- Contenido principal -->
        <div :class="image ? 'col-span-4 px-4' : 'col-span-5 px-4'">
          <div class="flex justify-between items-center py-2">
            <div>
              <h3 class="text-2xl font-bold mb-4">{{ name }}</h3>
              <p v-if="description" class="text-gray-600 mb-6">
                {{ description }}
              </p>
            </div>
            <div>
              <span
                v-if="status"
                class="px-5 py-1 text-sm rounded-full bg-green-100 text-green-800"
              >
                {{ status }}
              </span>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-800">
            <div v-for="(item, index) in properties" :key="index">
              <p class="text-gray-600">{{ item.label }}</p>
              <p class="font-semibold">{{ item.value || '-' }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'

type Property = {
  label: string
  value: string | number | null | undefined
  bold?: boolean
  uppercase?: boolean
}

const props = defineProps({
  icon: {
    type: String,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: false
  },
  status: {
    type: String,
    required: false
  },
  properties: {
    type: Array as PropType<Property[]>,
    required: true
  },
  image: {
    type: String,
    required: false
  }
})
</script>
