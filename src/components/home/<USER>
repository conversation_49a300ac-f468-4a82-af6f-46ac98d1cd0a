<template>
  <div class="navbar-menu flex flex-col w-full justify-center items-center h-4/5 dark:text-white">
    <p class="text-7xl text-center">Aplicació</p>
    <p class="text-7xl text-center mt-4"><PERSON><PERSON><PERSON> User Managment</p>
    <p class="text-primary mt-4 text-6xl">HOME</p>
    <p class="dark:text-white text-xl pt-10" v-if="account">
      Hello {{ account.name || 'Usuari no disponible' }}
    </p>
  </div>
</template>

<script lang="ts">
import { useAuthStore } from '@/stores/auth/index'
import type { IAccount } from '@/stores/auth/interfaces/account.interface'

export default {
  name: 'HeaderBar'
}
</script>

<script setup lang="ts">
import { computed, type ComputedRef } from 'vue'
const authStore = useAuthStore()
const account: ComputedRef<IAccount | undefined | null> = computed(() => authStore.account)
</script>
