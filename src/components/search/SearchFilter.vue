<template>
  <section v-if="filterOptions.length > 0" class="flex">
    <PwFilterDeferred
      :first-filter-options="filterOptions"
      :second-filter-options="secondFilterOptions"
      @firstFilter="selectSecondFilter"
      @submitFilter="filter"
      :filters="selectedFilters"
      :allow-search-without-filters="['applications', 'functions'].includes(hierarchyItemType)"
      class="w-full"
    ></PwFilterDeferred>
    <PwButton
      v-if="!hideCreateButton"
      @click="createRoute"
      :text="'Crear'"
      theme="primary-light"
      class="!my-0 ml-2 max-w-[130px] min-h-[56px] text-lg font-bold"
    >
      <font-awesome-icon icon="fa-solid fa-plus" class="max-w-[16px] mr-2" />
    </PwButton>
  </section>
</template>

<script lang="ts">
export default {
  name: 'FilterComponent'
}
</script>

<script setup lang="ts">
import { useHierarchyStore } from '@/stores/hierarchy'
import type { IHierarchyStore } from '@/stores/hierarchy/interfaces/store.interface'
import { PwButton, PwFilterDeferred } from 'parlem-webcomponents-common'
import { useRouter } from 'vue-router'
import type { ComputedRef, PropType, Ref } from 'vue'
import { ref, computed, onBeforeMount, watch } from 'vue'
import type { ISelectedFilterValue } from './interfaces/selectedFilterValue.interface'
import type { IPicklist } from '@/services/api/hierarchy/interfaces/picklist.interface'
import { GET_PICKLISTS } from '@/stores/hierarchy/constants/hierarchy.getters.constants'
import type { Router } from 'vue-router'
import getSearchFilter from '@/schema/search-filters-config/getSearchFilter'
import searchFilterConfig from '@/schema/search-filters-config/searchFiltersConfig.index'
import {
  FETCH_HIERARCHICAL_PICKLISTS,
  FETCH_PICKLISTS,
  GET_SELECTED_FILTERS
} from '@/stores/hierarchy/constants/hierarchy.actions.constants'
import type { IHierarchyType } from '@/stores/hierarchy/interfaces/hierarchy-type.interface'
import type { IFilter } from './interfaces/filter.interface'
import type { IFirstFilter } from './interfaces/firstFilter.interface'

const props = defineProps({
  hideCreateButton: {
    type: Boolean,
    default: false
  },
  hierarchyItemType: {
    type: String as PropType<IHierarchyType>,
    required: true
  },
  hierarchyItemKey: {
    type: String,
    required: true
  }
})

const hierarchyStore: IHierarchyStore = useHierarchyStore()
const router: Router = useRouter()
const picklists: ComputedRef<IPicklist[]> = computed(() => hierarchyStore[GET_PICKLISTS])
const filterOptions: Ref<IFirstFilter[]> = ref([])
const firstOptionSelected: Ref<IFirstFilter | undefined> = ref(undefined)
const secondFilterOptions: Ref<IFilter[]> = ref([])
const selectedFilters: ComputedRef<any[]> = computed(
  () => hierarchyStore[`${GET_SELECTED_FILTERS}`][`${props.hierarchyItemKey}`]
)

const emit = defineEmits<{
  isLoading: [value: boolean]
  filter: [value: ISelectedFilterValue[]]
}>()

onBeforeMount(async () => {
  await initSearchComponent()
})

watch(
  () => searchFilterConfig[props.hierarchyItemType],
  () => {
    updateFilterOptions()
  },
  { deep: true }
)

async function initSearchComponent() {
  emit('isLoading', true)
  if (!picklists.value || picklists.value?.length === 0) {
    await hierarchyStore[FETCH_PICKLISTS]()
  }
  updateFilterOptions()
  emit('isLoading', false)
}

function updateFilterOptions() {
  filterOptions.value = getSearchFilter(searchFilterConfig[props.hierarchyItemType]) || []
  firstOptionSelected.value = filterOptions.value[0]
  secondFilterOptions.value = firstOptionSelected.value?.options || []
}

function selectSecondFilter(value: string) {
  firstOptionSelected.value = filterOptions.value.find((option: IFilter) => option.value === value)
  if (firstOptionSelected.value) {
    secondFilterOptions.value = firstOptionSelected.value?.options || []
  }
}

async function filter(filterArray: ISelectedFilterValue[]) {
  const finalFilterArray = [...filterArray]

  hierarchyStore.saveSelectedFilters(props.hierarchyItemKey, finalFilterArray)
  emit('filter', finalFilterArray)
}

function createRoute(): void {
  router.push({ name: `${props.hierarchyItemType}-create-step1` })
}
</script>

<style></style>
