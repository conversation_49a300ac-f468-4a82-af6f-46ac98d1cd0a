export interface ISearchParams {
  orderBy: string
  orderDirection: string
  pageNum: number
  pageSize: number
  name?: string
  provider?: string
  providers?: string[]
  provisioningClass?: string
  packType?: string
  typePack?: string
  isProductive?: boolean
  isActive?: boolean
  startDate?: Date
  endDate?: Date
  type?: string
  subtype?: string
  provisioningSubClass?: string
  provisioningType?: string
  pimBusinessCode?: string
  coverageType?: string
  trademark?: string
  channel?: string
  [key: string]: string | undefined | boolean | Date | number | string[]
}
