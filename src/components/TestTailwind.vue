<template>
  <div class="p-4 space-y-4">
    <h2 class="text-2xl font-bold">Test de colors personalitzats de Tailwind</h2>
    
    <!-- Test bg-background-color -->
    <div class="bg-background-color p-4 border border-gray-300 rounded">
      <p>Aquest div hauria de tenir el color de fons background-color</p>
    </div>
    
    <!-- Test altres colors personalitzats -->
    <div class="bg-gray-light p-4 rounded">
      <p>Aquest div hauria de tenir el color gray-light</p>
    </div>
    
    <div class="bg-success p-4 text-white rounded">
      <p>Aquest div hauria de tenir el color success</p>
    </div>
    
    <div class="bg-error p-4 text-white rounded">
      <p>Aquest div hauria de tenir el color error</p>
    </div>
    
    <div class="bg-primary p-4 text-black rounded">
      <p>Aquest div hauria de tenir el color primary</p>
    </div>
    
    <!-- Test amb variables CSS directes -->
    <div style="background-color: var(--color-background)" class="p-4 border border-gray-300 rounded">
      <p>Aquest div utilitza directament var(--color-background)</p>
    </div>
    
    <div style="background-color: var(--color-success)" class="p-4 text-white rounded">
      <p>Aquest div utilitza directament var(--color-success)</p>
    </div>
  </div>
</template>

<script setup lang="ts">
// Component de prova per verificar els colors de Tailwind
</script>
