<template>
  <div class="max-w-screen-2xl">
    <pw-sidebar
      :tabs="tabs"
      :header="header"
      :footer="footer"
      @updateActiveTab="updateActiveTab"
      @tabRoute="routerInfo"
      @pinChanged="handlePinChanged"
    />
  </div>
</template>

<script lang="ts">
export default {
  name: 'SidebarComponent'
}
</script>

<script setup lang="ts">
import { PwSidebar } from 'parlem-webcomponents-common'
import { useAuthStore } from '@/stores/auth/index'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { computed, ref, watchEffect, watch, type ComputedRef } from 'vue'
import type { IAccount } from '@/stores/auth/interfaces/account.interface'
import SideBarTabs from '@/components/sidebar/SidebarTabs'

import type {
  ISidebarTab,
  ISidebarHeader,
  ISidebarFooter,
  ISidebarRouterObject,
  ISidebarUpdateActiveTabFunction
} from './interfaces/sidebar.interface'

const authStore = useAuthStore()
const router = useRouter()
const { t } = useI18n()

const account: ComputedRef<IAccount | undefined | null> = computed(() => authStore.account)

const emit = defineEmits(['sidebarPinnedChanged'])

const tabs = ref<ISidebarTab[]>(SideBarTabs)

const theme = localStorage.getItem('theme')

const baseLogoUrl = `${import.meta.env.VITE_LOGOS_URL}/parlem`
const parlemLogoLightkMode = `${baseLogoUrl}-logo-black.svg`
const parlemLogoDarkMode = `${baseLogoUrl}-logo-white.svg`
const parlemLogoSmall = `${baseLogoUrl}-logo-small.svg`

const header = ref<ISidebarHeader>({
  title: 'PUM',
  logo: theme === 'dark' ? parlemLogoDarkMode : parlemLogoLightkMode,
  logoSmall: parlemLogoSmall,
  pinUrl: `${import.meta.env.VITE_ICONS_URL}/pin-parlem.svg`,
  pinFillUrl: `${import.meta.env.VITE_ICONS_URL}/pin-filled-parlem.svg`
})

const footer = ref<ISidebarFooter>({
  name: t('sidebar-component.loading'),
  email: t('sidebar-component.loading')
  // logout: t('sidebar-component.logout')
})

const routerInfo = (routerObject: ISidebarRouterObject) => {
  router.push(routerObject.route)
}

const updateActiveTab: ISidebarUpdateActiveTabFunction = (selectedTab) => {
  const selectedTabName = typeof selectedTab === 'object' ? selectedTab.name : selectedTab

  tabs.value = tabs.value.map((tab: ISidebarTab) => {
    if (tab.withSubMenu && tab.subMenuTabs) {
      const isSubMenuActive = tab.subMenuTabs.some(
        (subMenuTab: ISidebarTab) => subMenuTab.name === selectedTabName
      )
      return {
        ...tab,
        isActive: tab.name === selectedTabName || isSubMenuActive,
        subMenuTabs: tab.subMenuTabs.map((subMenuTab: ISidebarTab) => ({
          ...subMenuTab,
          isActive: subMenuTab.name === selectedTabName
        }))
      }
    }

    return {
      ...tab,
      isActive: tab.name === selectedTabName
    }
  })
}

const isSidebarPinned = ref(false)

const handlePinChanged = (pinned: boolean) => {
  isSidebarPinned.value = pinned
}

watchEffect(() => {
  footer.value.name = account.value?.name ?? t('sidebar-component.not-available-name')
  footer.value.email = account.value?.username ?? t('sidebar-component.not-available-name')
  // footer.value.logout = account.value
  //   ? t('sidebar-component.logout')
  //   : t('sidebar-component.no-available')
})

watch(
  () => router.currentRoute.value.path,
  (newPath: string) => {
    tabs.value.forEach((tab) => {
      tab.isActive = tab.route === '/' ? newPath === '/' : newPath.startsWith(tab.route)
    })
  },
  { immediate: true }
)

watch(isSidebarPinned, (newValue) => {
  emit('sidebarPinnedChanged', newValue)
})
</script>
