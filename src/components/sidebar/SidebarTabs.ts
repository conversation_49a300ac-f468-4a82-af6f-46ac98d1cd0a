import type { ISidebarTab } from './interfaces/sidebar.interface'

const SideBarTabs: ISidebarTab[] = [
  { name: 'Home', icon: 'fa-home', route: '/', isActive: false },
  {
    name: 'Aplicacions',
    icon: 'fa-rocket',
    route: '/applications',
    isActive: false,
    withSubMenu: false,
    subMenuTabs: []
  },
  {
    name: 'R<PERSON>',
    icon: 'fa-person-walking-dashed-line-arrow-right',
    route: '/roles',
    isActive: false,
    withSubMenu: false,
    subMenuTabs: []
  },
  {
    name: 'Funcions',
    icon: 'fa-location-crosshairs',
    route: '/functions',
    isActive: false,
    withSubMenu: false,
    subMenuTabs: []
  },
  {
    name: 'Usuaris',
    icon: 'fa-user-tie',
    route: '/users',
    isActive: false,
    withSubMenu: false,
    subMenuTabs: []
  },
  {
    name: 'Perfils',
    icon: 'fa-user-shield',
    route: '/profiles',
    isActive: false,
    withSubMenu: false,
    subMenuTabs: []
  },
  {
    name: 'Comercial Dealers',
    icon: 'fa-users-viewfinder',
    route: '/dealers',
    isActive: false,
    withSubMenu: false,
    subMenuTabs: []
  },
  {
    name: 'Comercial Subdealers',
    icon: 'fa-people-group',
    route: '/subdealers',
    isActive: false,
    withSubMenu: false,
    subMenuTabs: []
  }
]

export default SideBarTabs
