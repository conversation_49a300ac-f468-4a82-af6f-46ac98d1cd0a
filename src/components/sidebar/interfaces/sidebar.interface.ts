export interface ISidebarTab {
  name: string
  icon: string
  route: string
  isActive: boolean
  withSubMenu?: boolean
  subMenuTabs?: ISidebarTab[]
}

export interface ISidebarHeader {
  title: string
  logo: string
  logoSmall: string
  pinUrl: string
  pinFillUrl: string
}

export interface ISidebarFooter {
  name: string
  email: string
  logout?: string
}

export interface ISidebarRouterObject {
  route: string
}
export interface ISidebarUpdateActiveTabFunction {
  (selectedTab: string | ISidebarTab): void;
}
