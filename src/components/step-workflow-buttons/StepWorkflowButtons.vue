<template>
  <div class="flex justify-end">
    <PwButton
      @click="goBack"
      :text="currentStep?.backButtonLabel ? currentStep.backButtonLabel : 'Enrera'"
      theme="outline-secondary"
      class="!my-0 max-w-[140px] max-h-[36px]"
    >
    </PwButton>
    <PwButton
      @click="goNext"
      :text="currentStep?.nextButtonLabel ? currentStep.nextButtonLabel : 'Endavant'"
      :disabled="isValidForm"
      theme="primary-white"
      class="!my-0 ml-2 max-w-[140px] max-h-[36px]"
    >
    </PwButton>
  </div>
</template>

<script setup lang="ts">
import type { IStep } from '@/schema/stepper-config/interfaces/step.interface'
import { useWorkflowStore } from '@/stores/workflow'
import { PwButton } from 'parlem-webcomponents-common'
import { computed, ref, type ComputedRef, type Ref } from 'vue'

defineProps({
  isValidForm: {
    type: Boolean,
    default: false
  }
})
const workflowStore = useWorkflowStore()
const currentStep: ComputedRef<IStep | null> = computed(() => workflowStore.getCurrentStep)
const currentStepNumber: ComputedRef<number> = computed(() => workflowStore.getCurrentStepNumber)

const goBack = () => {
  changeCurrentStep(currentStepNumber.value - 1)
}

const goNext = () => {
  changeCurrentStep(currentStepNumber.value + 1)
}

const changeCurrentStep = (stepNumber: number) => {
  workflowStore.setCurrentStep(stepNumber)
}
</script>
<style></style>
