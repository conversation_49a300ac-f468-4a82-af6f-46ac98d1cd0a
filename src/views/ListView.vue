<template>
  <ListWithSearch
    :hierarchyItemType="hierarchyItemType"
    :hierarchyItemKey="hierarchyItemKey"
  ></ListWithSearch>
</template>

<script lang="ts">
export default {
  name: 'ListView'
}
</script>

<script setup lang="ts">
import ListWithSearch from '@/components/list-search/ListWithSearch.vue'
import type { IHierarchyType } from '@/stores/hierarchy/interfaces/hierarchy-type.interface'
import type { PropType } from 'vue'

const props = defineProps({
  hierarchyItemType: {
    type: String as PropType<IHierarchyType>,

    required: true
  },
  hierarchyItemKey: {
    type: String,
    required: true
  }
})
</script>
