<template>
  <div class="applications-view">
    <SearchFilter
      :hierarchyItemType="hierarchyItemType"
      :hierarchyItemKey="hierarchyItemKey"
      @filter="filter"
      @is-loading="isLoading = $event"
    ></SearchFilter>

    <div v-if="isLoading" class="mt-20 h-1/2 flex flex-col justify-center items-center">
      <PwLoading
        company="Parlem"
        loading-style="w-40 h-40"
        loading-image-style="w-[24px]"
      ></PwLoading>
      <p class="mt-4 text-lg text-gray animate-pulse">Carregant...</p>
    </div>

    <!-- Content when not loading -->
    <div v-else class="flex flex-col">
      <!-- Lista de applications -->
      <div v-if="searchItems?.length" class="flex-1">
        <h3 class="text-lg font-bold dark:text-white mt-4">Aplicacions</h3>
        <div class="w-full overflow-y-scroll pb-20 llista-no-filter">
          <div class="py-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-2">
            <PwCard
              v-for="(application, index) in searchItems"
              :key="(application as any).id || index"
              :image="'https://parlem.com/wp-content/uploads/2025/05/tienda-parlem-1024x683.jpg'"
              :title="(application as any).name || `Aplicació ${index + 1}`"
              :accessButton="{
                text: 'Accedir',
                theme: 'primary',
                onClick: () => handleAccess(application as any, index)
              }"
              :menu="[
                {
                  name: 'Editar',
                  icon: 'pen',
                  onClick: () => handleEdit(application as any),
                  show: true
                },
                {
                  name: 'Eliminar',
                  icon: 'trash',
                  onClick: () => handleDelete(application as any),
                  show: true
                }
              ]"
            />
          </div>
        </div>
        <Pagination
          :isLoading="isLoading"
          :searchItemsLength="searchItems.length"
          :showMoreItemsTab="showGetMoreItemsButton()"
          @changePageSize="handleChangePageSize"
          @get-more-items="getMoreItems"
        ></Pagination>
      </div>

      <!-- Empty state -->
      <div v-else class="flex items-center px-12 pt-5 grid grid-cols-5 gap-2 h-3/6">
        <div class="pl-6 col-span-2">
          <p class="text-5xl font-bold text-black dark:text-white">FILTRA PER COMENÇAR</p>
          <p class="text-lg text-dark-gray dark:text-gray mt-2">
            Aplica un filtre per obtenir un llistat de
            {{ $t(`item-type.${hierarchyItemType}`).toLowerCase() }}
          </p>
        </div>
        <div class="flex justify-center col-span-3">
          <img
            src="/src/assets/svg/web-search-cuate.svg"
            alt="il·lustració d'image de búsqueda."
            class="w-9/12"
          />
        </div>
      </div>
    </div>

    <PwPopupFullScreen
      v-if="showDetailPopup"
      :closeButton="true"
      :expandButton="true"
      :cancelButton="false"
      :acceptButton="false"
      @close="closeDetailPopup"
      :initialExpanded="true"
    >
      <ApplicationDetail
        :applicationId="selectedApplicationId"
        :applicationData="selectedApplicationData"
        @edit="handleEditFromDetail"
        @delete="handleDeleteFromDetail"
        @close="closeDetailPopup"
      />
    </PwPopupFullScreen>

    <PopUp
      v-if="showDeletePopup"
      :visible="showDeletePopup"
      :message="`Estàs segur que vols eliminar l’aplicació ${applicationToDelete.value!?.name || ''}?`"
      iconKey="delete"
      @accept="confirmDelete"
      @cancel="showDeletePopup = false"
      @close="showDeletePopup = false"
    />
  </div>
</template>

<script lang="ts">
export default {
  name: 'ApplicationsView'
}
</script>

<script setup lang="ts">
import SearchFilter from '@/components/search/SearchFilter.vue'
import Pagination from '@/components/pagination/Pagination.vue'
import PopUp from '@/components/popup/PopUp.vue'

import type { ComputedRef, PropType, Ref } from 'vue'
import { computed, ref, onMounted } from 'vue'
import type { ISearchParams } from '@/components/search/interfaces/search.interface'
import type { ISelectedFilterValue } from '@/components/search/interfaces/selectedFilterValue.interface'
import { useHierarchyStore } from '@/stores/hierarchy'
import type { IHierarchyStore } from '@/stores/hierarchy/interfaces/store.interface'
import type { IHierarchyType } from '@/stores/hierarchy/interfaces/hierarchy-type.interface'
import { PwLoading, PwCard, PwPopupFullScreen } from 'parlem-webcomponents-common'

// Tipus per les aplicacions
interface Application {
  id: string | number
  name: string
  description?: string
  [key: string]: unknown
}

// Lazy load ApplicationDetail ja que només es carrega quan es necessita
const ApplicationDetail = () => import('@/modules/applications/components/ApplicationDetail.vue')

const props = defineProps({
  hierarchyItemType: {
    type: String as PropType<IHierarchyType>,
    required: true
  },
  hierarchyItemKey: {
    type: String,
    required: true
  }
})

const hierarchyStore: IHierarchyStore = useHierarchyStore()
const hasFiltersSelected: Ref<boolean> = ref(false)
const searchItems: ComputedRef<unknown[]> = computed(
  () => hierarchyStore.searchItems[`${props.hierarchyItemKey}`] || []
)
const isLoading: Ref<boolean> = ref(false)
const searchParams: Ref<ISearchParams> = ref({
  orderBy: 'id',
  orderDirection: 'desc',
  pageNum: 0,
  pageSize: 50
})

onMounted(async () => {
  await getItems()
})

// Variables para el popup de detalles
const showDetailPopup = ref(false)
const selectedApplicationId = ref<string | number | undefined>(undefined)
const selectedApplicationData = ref<Application>({} as Application)

const showDeletePopup = ref(false)
const applicationToDelete = ref<any>(null)

function handleDelete(application: Application) {
  applicationToDelete.value = application
  showDeletePopup.value = true
}

function confirmDelete() {
  // Aquí puedes llamar a un método de eliminación en el store o API

  showDeletePopup.value = false
  applicationToDelete.value = null
}

// La paginación ahora se maneja directamente por la API

async function filter(filterArray: ISelectedFilterValue[]) {
  isLoading.value = true
  hasFiltersSelected.value = filterArray.length > 0
  searchParams.value = {
    orderBy: searchParams.value.orderBy,
    orderDirection: searchParams.value.orderDirection,
    pageNum: searchParams.value.pageNum,
    pageSize: searchParams.value.pageSize
  }
  if (filterArray.length > 0) {
    filterArray.forEach(
      (filter: ISelectedFilterValue) =>
        (searchParams.value[filter.firstFilter] = filter.secondFilter)
    )
  } else {
    hierarchyStore.removeSearchItems(props.hierarchyItemKey)
  }
  await getItems()

  isLoading.value = false
}

async function getItems(): Promise<void> {
  isLoading.value = true
  searchParams.value.pageNum = 0

  await hierarchyStore.getItemsBySearch(
    props.hierarchyItemType,
    props.hierarchyItemKey,
    searchParams.value
  )

  isLoading.value = false
}

function handleChangePageSize(tab: any): void {
  isLoading.value = true
  const pageSize = parseInt(tab.label)
  searchParams.value.pageSize = pageSize
  getItems()

  isLoading.value = false
}

async function getMoreItems(): Promise<void> {
  isLoading.value = true
  ++searchParams.value.pageNum

  await hierarchyStore.getItemsBySearch(
    props.hierarchyItemType,
    props.hierarchyItemKey,
    searchParams.value
  )

  isLoading.value = false
}

const showGetMoreItemsButton = (): boolean => {
  return searchItems.value.length >= searchParams.value.pageNum + 1 * searchParams.value.pageSize
}

// Funciones para las tarjetas
function handleAccess(application: any, index: number) {
  console.log(`Accediendo a la aplicación:`, application)

  // Configurar los datos para el popup
  selectedApplicationId.value = application?.id || index + 1
  selectedApplicationData.value = application || {}

  // Mostrar el popup
  showDetailPopup.value = true
}

function handleEdit(application: any) {
  console.log(`Editando aplicación:`, application)
  // Aquí se podría navegar a la ruta de edición
  // router.push({ name: 'applications-edit-step1', params: { id: application.id } })
}

// Funciones para el popup de detalles
function closeDetailPopup() {
  showDetailPopup.value = false
  selectedApplicationId.value = undefined
  selectedApplicationData.value = {} as Application
}

function handleEditFromDetail() {
  console.log('Editando desde el detalle')
  // Aquí se podría navegar a la ruta de edición
  closeDetailPopup()
}

function handleDeleteFromDetail() {
  console.log('Eliminando desde el detalle')
  // Aquí se podría mostrar un modal de confirmación
  closeDetailPopup()
}
</script>

<style scoped>
.llista {
  height: calc(100vh - 300px);
}
.llista-no-filter {
  height: calc(100vh - 225px);
}

.applications-custom-section {
  min-height: 120px;
  border: 1px solid #e5e7eb;
}

.dark .applications-custom-section {
  border-color: #374151;
}
</style>
