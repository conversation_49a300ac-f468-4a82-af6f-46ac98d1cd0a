<template>
  <Breadcrumb />
  <RouterView />
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Breadcrumb from '@/components/breadcrumb/Breadcrumb.vue'
import { useHierarchyStore } from '@/stores/hierarchy'
import type { IHierarchyStore } from '@/stores/hierarchy/interfaces/store.interface'

const hierarchyStore: IHierarchyStore = useHierarchyStore()
const route = useRoute()
const router = useRouter()

onMounted(() => {
  router.push(route.path)
})
</script>
