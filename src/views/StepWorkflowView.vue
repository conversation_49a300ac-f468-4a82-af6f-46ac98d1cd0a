<template>
  <PwLoading
    v-if="isLoading"
    company="Parlem"
    loading-style="w-40 h-40"
    loading-image-style="w-[24px]"
    class="mt-10"
  ></PwLoading>
  <div v-else class="flex flex-col min-h-[calc(100vh-120px)] justify-between">
    <div :class="['flex flex-col w-full']">
      <PwStepperMinimalist theme="primary" :Number="currentStepNumber" :steps="steps ? steps : []">
      </PwStepperMinimalist>
      <h2 v-if="steps?.[currentStepNumber - 1]" class="text-xl mt-8 mb-3 font-medium">
        {{ currentStepNumber }}. {{ route.meta.title?.toUpperCase() }}
      </h2>
      <RouterView />
    </div>
    <StepWorkflowButtons></StepWorkflowButtons>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ListView'
}
</script>

<script setup lang="ts">
import { PwStepperMinimalist, PwLoading } from 'parlem-webcomponents-common'
import type { IHierarchyType } from '@/stores/hierarchy/interfaces/hierarchy-type.interface'
import { onMounted, ref, type PropType, type Ref, computed, type ComputedRef } from 'vue'
import type { IStep } from '@/schema/stepper-config/interfaces/step.interface'
import stepperConfig from '@/schema/stepper-config/stepperConfig.index'
import { useRoute } from 'vue-router'
import StepWorkflowButtons from '@/components/step-workflow-buttons/StepWorkflowButtons.vue'
import { useWorkflowStore } from '@/stores/workflow'
import type { IWorkflowStore } from '@/stores/workflow/interfaces/store.interface'

const props = defineProps({
  hierarchyItemType: {
    type: String as PropType<IHierarchyType>,

    required: true
  },
  hierarchyItemKey: {
    type: String,
    required: true
  }
})
const workflowStore: IWorkflowStore = useWorkflowStore()
const isLoading: Ref<boolean> = ref(false)
const route = useRoute()
const steps: Ref<IStep[] | null> = ref(null)
const currentStepNumber: ComputedRef<number> = computed(() => workflowStore.getCurrentStepNumber)
const isEditRoute = ref(false)

onMounted(() => {
  isLoading.value = true
  isEditRoute.value = route.params.id ? true : false
  getSteps()
  isLoading.value = false
})

const getSteps = () => {
  steps.value = stepperConfig[props.hierarchyItemType][isEditRoute.value ? 'edit' : 'create']
  workflowStore.setWorkflowSteps(steps.value)
}
</script>
