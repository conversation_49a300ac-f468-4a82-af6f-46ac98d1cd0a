import { useAuthStore } from '@/stores/auth/index'
import type { IAccount } from '@/stores/auth/interfaces/account.interface'
import { checkUserCredentialsMsal } from 'parlem-webcomponents-common'
import type { IPublicClientApplication } from '@azure/msal-browser'

export default async function checkUserCredentials(): Promise<IAccount | null> {
  const authStore = useAuthStore()
  authStore.setAuthenticationError(false)

  try {
    const msalInstance: IPublicClientApplication | undefined =
      (window as any).msalInstance ?? authStore.msalInstance

    const msalResponse = await checkUserCredentialsMsal(msalInstance)

    authStore.setMsalInstance(msalResponse.msalInstance)
    ;(window as any).msalInstance = msalResponse.msalInstance

    const accessTokenResponse = msalResponse.result

    if (accessTokenResponse && accessTokenResponse.idToken) {
      const accessToken: string = accessTokenResponse.idToken
      const account: IAccount = accessTokenResponse.account

      authStore.setAccessToken(accessToken)
      authStore.setAccount(account)

      if (!account.username) {
        authStore.setAuthenticationError(true)
        return null
      }

      return account
    } else {
      authStore.setAuthenticationError(true)
      return null
    }
  } catch (error) {
    console.error(`Error during authentication: ${error}`)
    authStore.setAuthenticationError(true)
    return null
  }
}
