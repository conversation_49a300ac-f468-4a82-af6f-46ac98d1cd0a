import { useToast } from 'vue-toast-notification'
import 'vue-toast-notification/dist/theme-sugar.css'

// Importar la funció segons notificació (success/error/warning) i missatge
// 1) s'importa així: import { toastUtils } from 'ruta corresponent';
// 2) toastUtils.showToast('success', 'missatge');

export const toastUtils = {
  showToast(type: 'success' | 'error' | 'warning', message: string) {
    const $toast = useToast()
    if (type === 'success') {
      $toast.success(message, {
        duration: 3000,
        position: 'bottom-right',
        dismissible: true
      })
    } else if (type === 'error') {
      $toast.error(message, {
        duration: 10000,
        position: 'bottom-right',
        dismissible: true
      })
    } else if (type === 'warning') {
      $toast.warning(message, {
        duration: 10000,
        position: 'bottom-right',
        dismissible: true
      })
    }
  }
}
