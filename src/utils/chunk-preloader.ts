// Precarrega chunks crítics per millorar el rendiment
export const preloadCriticalChunks = () => {
  // Precarrega ag-grid quan l'usuari navega a una pàgina amb llistes
  const preloadAgGrid = () => {
    import('ag-grid-vue3')
    import('ag-grid-community')
  }

  // Precarrega components de llista quan sigui probable que es necessitin
  const preloadListComponents = () => {
    import('@/components/list/List.vue')
    import('@/components/list-search/ListWithSearch.vue')
  }

  // Detecta si l'usuari està en una ruta que probablement necessitarà llistes
  const currentPath = window.location.pathname
  if (
    currentPath.includes('/roles') ||
    currentPath.includes('/users') ||
    currentPath.includes('/applications')
  ) {
    preloadAgGrid()
    preloadListComponents()
  }
}

// Crida aquesta funció després de la càrrega inicial
export const initChunkPreloader = () => {
  // Espera que la pàgina estigui carregada
  if (document.readyState === 'complete') {
    preloadCriticalChunks()
  } else {
    window.addEventListener('load', preloadCriticalChunks)
  }
}
