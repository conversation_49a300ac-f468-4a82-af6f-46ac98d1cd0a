import apiHierarchyService from '@/services/api/hierarchy/apiHierarchyService'
import type { IHierarchyStore } from './interfaces/store.interface'
import type { IHierarchyType } from './interfaces/hierarchy-type.interface'
import type { IFilterRes } from '@/services/api/hierarchy/interfaces/filter-res.interface'
import type { ISearchParams } from '@/components/search/interfaces/search.interface'
import type { IPicklist } from '@/services/api/hierarchy/interfaces'
import type { ISelectedFilterValue } from '@/components/search/interfaces/selectedFilterValue.interface'
import type { RoleResponse } from '@/services/api/hierarchy/interfaces/role.interface'

//_______GET HIERARCHY PICKLISTS________
export async function fetchPicklists(this: IHierarchyStore): Promise<void> {
  try {
    const picklists: IPicklist[] = await apiHierarchyService.getPicklists()
    if (picklists?.length) {
      this.picklists = [...this.picklists, ...picklists]
    }
    this.picklistsError = false
  } catch (error) {
    console.error('Error:', error)
    this.picklistsError = true
  }
}

export async function fetchHierarchicalPicklists(this: IHierarchyStore): Promise<void> {
  try {
    const hierarchicalPicklists: IPicklist[] = await apiHierarchyService.getHierarchicalPicklists()
    if (hierarchicalPicklists?.length) {
      this.picklists = [...this.picklists, ...hierarchicalPicklists]
    }
    this.picklistsError = false
  } catch (error) {
    console.error('Error:', error)
    this.picklistsError = true
  }
}

export function setPicklistsError(this: IHierarchyStore, value: boolean): void {
  this.picklistsError = value
}

//_______GET ITEMS BY SEARCH________
export async function getItemsBySearch<T>(
  this: IHierarchyStore,
  hierarchyItemType: IHierarchyType,
  hierarchyItemKey: string,
  searchParams: ISearchParams
): Promise<void> {
  const hierarchyItems: IFilterRes<T[]> | void = await apiHierarchyService.getItemsBySearch(
    hierarchyItemType,
    searchParams
  )

  if (!this.searchItems[hierarchyItemKey]) {
    this.searchItems[hierarchyItemKey] = []
  }

  if (hierarchyItems) {
    this.searchItems[hierarchyItemKey] =
      searchParams.pageNum > 0
        ? [...this.searchItems[hierarchyItemKey], ...hierarchyItems.results]
        : hierarchyItems.results
  }
}

export function addRowChildren<T extends { id: number | string }>(
  this: IHierarchyStore,
  hierarchyItemKey: string,
  parentIndex: number,
  parentItem: T
): void {
  this.searchItems[hierarchyItemKey].splice(parentIndex + 1, 0, {
    isChild: true,
    parentId: parentItem.id,
    parentItem
  })
}

export function removeRowChildren<T extends { id: number | string }>(
  this: IHierarchyStore,
  hierarchyItemKey: string,
  parentItem: T
): void {
  this.searchItems[hierarchyItemKey] = this.searchItems[hierarchyItemKey].filter(
    (item: unknown) => {
      const typedItem = item as { isChild?: boolean; parentId?: string | number }
      return !typedItem.isChild || (typedItem.isChild && typedItem.parentId !== parentItem.id)
    }
  )
}

export function saveSelectedFilters(
  this: IHierarchyStore,
  hierarchyItemKey: string,
  filters: ISelectedFilterValue[]
): void {
  this.selectedFilters[hierarchyItemKey] = filters
}

export function removeSearchItems(this: IHierarchyStore, hierarchyItemKey: string): void {
  this.searchItems[hierarchyItemKey] = []
}

export async function getItemById<T>(
  this: IHierarchyStore,
  hierarchyItemType: IHierarchyType,
  id: string | number
): Promise<T | void> {
  try {
    const response = await apiHierarchyService.getItemById<T>(hierarchyItemType, String(id))
    this.itemById = response as RoleResponse

    return response
  } catch (error) {
    console.error('Error fetching item by id:', error)
  }
}
