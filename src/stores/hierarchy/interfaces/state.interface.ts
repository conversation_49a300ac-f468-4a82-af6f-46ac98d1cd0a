import type { ISelectedFilterValue } from '@/components/search/interfaces/selectedFilterValue.interface'
import type { IPicklist } from '@/services/api/hierarchy/interfaces'
import type { RoleResponse } from '@/services/api/hierarchy/interfaces/role.interface'

export interface IHierarchyState {
  picklists: IPicklist[]
  picklistsError: boolean
  searchItems: { [key: string]: unknown[] }
  selectedFilters: { [key: string]: ISelectedFilterValue[] }
  itemById: RoleResponse
}
