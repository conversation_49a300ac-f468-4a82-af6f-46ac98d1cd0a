import type { ISelectedFilterValue } from '@/components/search/interfaces/selectedFilterValue.interface'
import type { IPicklist } from '@/services/api/hierarchy/interfaces'
import type { IHierarchyState } from '@/stores/hierarchy/interfaces/state.interface'

export const getPicklists = (state: IHierarchyState): IPicklist[] => {
  return state.picklists
}

export const getSearchItems = (state: IHierarchyState): unknown => {
  return state.searchItems
}

export const getSelectedFilters = (
  state: IHierarchyState
): ((hierarchyItemKey: string) => ISelectedFilterValue[]) => {
  return (hierarchyItemKey: string) => {
    return state.selectedFilters[hierarchyItemKey]
  }
}
