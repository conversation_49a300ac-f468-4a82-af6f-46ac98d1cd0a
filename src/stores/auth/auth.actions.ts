import type { IAccount } from './interfaces/account.interface'
import type { IAuthStore } from './interfaces/store.interface'
import type { IPublicClientApplication } from '@azure/msal-browser'

export function setAccessToken(this: IAuthStore, accessToken: string) {
  this.accessToken = accessToken
}
export function setAccount(this: IAuthStore, account: IAccount | null) {
  this.account = account
}
export function setAuthenticationError(this: IAuthStore, authenticationError: boolean) {
  this.authenticationError = authenticationError
}
export function setMsalInstance(this: IAuthStore, msalInstance: IPublicClientApplication) {
  this.msalInstance = msalInstance
}
