import type { ISelectedFilterValue } from '@/components/search/interfaces/selectedFilterValue.interface'
import type { IStep } from '@/schema/stepper-config/interfaces/step.interface'

export interface IWorkflowState {
  selectedFilters: { [key: string]: ISelectedFilterValue[] }
  currentStep: IStep | null
  currentStepNumber: number
  stepper: IStep[] | null
  openListChildren: { [key: string]: string[] }
}
