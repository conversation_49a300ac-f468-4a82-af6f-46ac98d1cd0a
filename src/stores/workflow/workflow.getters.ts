import type { ISelectedFilterValue } from '@/components/search/interfaces/selectedFilterValue.interface'
import type { IWorkflowState } from './interfaces/state.interface'
import type { IStep } from '@/schema/stepper-config/interfaces/step.interface'

//pendent fer el traspas bé a workflow
/* export const getSelectedFilters = (
  state: IWorkflowState
): ((hierarchyItemKey: string) => ISelectedFilterValue[]) => {
  return (hierarchyItemKey: string) => {
    return state.selectedFilters[hierarchyItemKey]
  }
} */

export const getCurrentStep = (state: IWorkflowState): IStep | null => {
  return state.currentStep
}

export const getCurrentStepNumber = (state: IWorkflowState): number => {
  return state.currentStepNumber
}

export const getOpenListChildren = (state: IWorkflowState): { [key: string]: string[] } => {
  return state.openListChildren
}
