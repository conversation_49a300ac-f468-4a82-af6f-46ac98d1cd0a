import type { IStep } from '@/schema/stepper-config/interfaces/step.interface'
import type { IWorkflowStore } from './interfaces/store.interface'
import type { ISelectedFilterValue } from '@/components/search/interfaces/selectedFilterValue.interface'
import type { IHierarchyType } from '../hierarchy/interfaces/hierarchy-type.interface'

//pendent fer el traspas bé a workflow
/* export function saveSelectedFilters(
  this: IWorkflowStore,
  hierarchyItemKey: string,
  filters: ISelectedFilterValue[]
): void {
  this.selectedFilters[hierarchyItemKey] = filters
} */

export function setCurrentStep(this: IWorkflowStore, currentStepNumber: number): void {
  this.currentStepNumber = currentStepNumber
  this.currentStep = this.stepper?.find((step) => +step.label === currentStepNumber) || null
}

export function setWorkflowSteps(this: IWorkflowStore, workflowSteps: IStep[] | null): void {
  this.stepper = workflowSteps
  this.currentStep = workflowSteps ? workflowSteps[0] : null
}

export function addChildrenToOpenList(
  this: IWorkflowStore,
  hierarchyItemKey: string,
  childId: string
): void {
  if (!this.openListChildren[hierarchyItemKey]) {
    this.openListChildren[hierarchyItemKey] = []
  }
  this.openListChildren[hierarchyItemKey] = [...this.openListChildren[hierarchyItemKey], childId]
}

export function deleteChildrenFromOpenList(
  this: IWorkflowStore,
  hierarchyItemKey: string,
  childId: string
): void {
  this.openListChildren[hierarchyItemKey] = this.openListChildren[hierarchyItemKey].filter(
    (id) => id !== childId
  )
}
