import axios, { isAxiosError } from 'axios'
import {
  CONTENT_TYPE,
  APPLICATION_JSON_PATCH_JSON,
  API,
  BEARER,
  AUTHORIZATION,
  AZUREAD
} from '@/services/constants/constants'
import {
  HIERARCHY,
  PICKLISTS,
  HIERARCHIC<PERSON>PICKLISTS,
  SEARCHES,
  DISABLINGSTATES
} from '@/services/api/hierarchy/constants/hierarchy.constants'
import type { IApiRes, IHeaders } from '../../interfaces'
import type { IApiHierarchy, IPicklist } from './interfaces'
import type { ISearchParams } from '@/components/search/interfaces/search.interface'
import { useAuthStore } from '@/stores/auth/index'
import type { IHierarchyType } from '@/stores/hierarchy/interfaces/hierarchy-type.interface'
import { toastUtils } from '@/utils/notifications/toastNotifications'

function createHierarchyHeader() {
  const authStore = useAuthStore()
  const token = authStore.accessToken

  const hierarchyHeaders: IHeaders = {
    headers: {
      [CONTENT_TYPE]: APPLICATION_JSON_PATCH_JSON,
      [AUTHORIZATION]: `${BEARER}${token}`,
      'Access-Control-Allow-Origin': '*'
    }
  }
  return hierarchyHeaders
}

const apiHierarchyService: IApiHierarchy = {
  async getPicklists(): Promise<IPicklist[]> {
    try {
      const url: string = `${import.meta.env.VITE_BASE_URL}/${AZUREAD}/${HIERARCHY}/${API}/${PICKLISTS}`
      const response: IApiRes<IPicklist[]> = await axios.get(url, createHierarchyHeader())
      return response && response.data
    } catch (error: unknown) {
      if (isAxiosError(error) && error.response) {
        console.error(error.response)
      }
      return []
    }
  },

  async getHierarchicalPicklists(): Promise<IPicklist[]> {
    try {
      const url: string = `${
        import.meta.env.VITE_BASE_URL
      }/${AZUREAD}/${HIERARCHY}/${API}/${HIERARCHICALPICKLISTS}`
      const response: IApiRes<IPicklist[]> = await axios.get(url, createHierarchyHeader())

      return response && response.data
    } catch (error: unknown) {
      if (isAxiosError(error) && error.response) {
        console.error(error.response)
      }
      return []
    }
  },

  async getItemById<T>(hierarchyItemType: IHierarchyType, id: string): Promise<T | void> {
    try {
      const url: string = `${
        import.meta.env.VITE_BASE_URL
      }/${AZUREAD}/${HIERARCHY}/${API}/${hierarchyItemType.toLowerCase()}/${id}`
      const response: IApiRes<T> = await axios.get(url, createHierarchyHeader())

      return response.data
    } catch (error: unknown) {
      if (isAxiosError(error) && error.response) {
        toastUtils.showToast('error', `Hi ha hagut un error. No s'ha pogut obtenir el detall.`)
        return
      }
    }
  },

  async getItemsBySearch<T>(
    hierarchyItemType: IHierarchyType,
    searchParams: ISearchParams
  ): Promise<T | void> {
    try {
      const url: string = `${import.meta.env.VITE_BASE_URL}/${AZUREAD}/${HIERARCHY}/${API}/${hierarchyItemType.toLowerCase()}/${SEARCHES}`

      const response: IApiRes<T> = await axios.post(url, searchParams, createHierarchyHeader())

      return response.data
    } catch (error: unknown) {
      if (isAxiosError(error) && error.response) {
        toastUtils.showToast(
          'error',
          `Hi ha hagut un error. No s'ha pogut obtenir el llistat de ${hierarchyItemType} amb el filtre seleccionat${
            error.response.data
              ? typeof error.response.data === 'string'
                ? ': \n' + error.response.data
                : ': \n' + error.response.data.title
              : ''
          }`
        )
      }
    }
  },

  //CRUD
  async createItem<T>(hierarchyItemType: IHierarchyType, itemObject: any): Promise<T | void> {
    try {
      const url: string = `${
        import.meta.env.VITE_BASE_URL
      }/${AZUREAD}/${HIERARCHY}/${API}/${hierarchyItemType.toLowerCase()}`
      const response: IApiRes<T> = await axios.post(url, itemObject, createHierarchyHeader())
      toastUtils.showToast('success', `Perfecte, S'ha creat correctament! :)`)
      return response.data
    } catch (error: unknown) {
      if (isAxiosError(error) && error.response) {
        /*     const crudStore = useCrudStore()
        crudStore.showModalWithMessage(error.response.data) */
        if (error.response.status === 401 || error.response.status === 403) {
          toastUtils.showToast(
            'error',
            'No estàs autoritzat per fer aquesta acció o la sessió ha expirat.'
          )
        } else if (error.response.status === 500) {
          toastUtils.showToast(
            'error',
            'Hi ha hagut un error intern al servidor. Torna-ho a intentar més tard.'
          )
        } else if (error.response.status === 404) {
          toastUtils.showToast('error', "L'element no s'ha trobat.")
        }
        return
      }
    }
  },
  async updateItem<T>(
    hierarchyItemType: IHierarchyType,
    itemObject: any,
    id: string
  ): Promise<T | void> {
    try {
      const url: string = `${
        import.meta.env.VITE_BASE_URL
      }/${AZUREAD}/${HIERARCHY}/${API}/${hierarchyItemType.toLowerCase()}/${id}`
      const response: IApiRes<T> = await axios.put(url, itemObject, createHierarchyHeader())
      toastUtils.showToast('success', `Perfecte, S'ha editat correctament! :)`)
      return response.data
    } catch (error: unknown) {
      if (isAxiosError(error) && error.response) {
        /*     const crudStore = useCrudStore()
        crudStore.showModalWithMessage(error.response.data) */
        if (error.response.status === 401 || error.response.status === 403) {
          toastUtils.showToast(
            'error',
            'No estàs autoritzat per fer aquesta acció o la sessió ha expirat.'
          )
        } else if (error.response.status === 500) {
          toastUtils.showToast(
            'error',
            'Hi ha hagut un error intern al servidor. Torna-ho a intentar més tard.'
          )
        } else if (error.response.status === 404) {
          toastUtils.showToast('error', "L'element no s'ha trobat.")
        }
        return
      }
    }
  },
  async disablingStatesItem<T>(hierarchyItemType: IHierarchyType, id: string): Promise<T | void> {
    try {
      const url: string = `${
        import.meta.env.VITE_BASE_URL
      }/${AZUREAD}/${HIERARCHY}/${API}/${hierarchyItemType.toLowerCase()}/${id}/${DISABLINGSTATES}`
      const response: IApiRes<T> = await axios.post(url, {}, createHierarchyHeader())
      toastUtils.showToast('success', `Perfecte, l'element s'ha borrat correctament! :)`)
      return response.data
    } catch (error: unknown) {
      if (isAxiosError(error) && error.response) {
        /*    const crudStore = useCrudStore()
        crudStore.showModalWithMessage(error.response.data)
 */
        if (error.response.status === 401 || error.response.status === 403) {
          toastUtils.showToast(
            'error',
            'No estàs autoritzat per fer aquesta acció o la sessió ha expirat.'
          )
        } else if (error.response.status === 500) {
          toastUtils.showToast(
            'error',
            'Hi ha hagut un error intern al servidor. Torna-ho a intentar més tard.'
          )
        } else if (error.response.status === 404) {
          toastUtils.showToast('error', "L'element no s'ha trobat.")
        }
        return error.response.data
      }
    }
  }
}

export default apiHierarchyService
