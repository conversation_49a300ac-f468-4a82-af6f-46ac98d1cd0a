import type { ISearchParams } from '@/components/search/interfaces/search.interface'
import type { IPicklist } from './picklist.interface'
import type { IHierarchyType } from '@/stores/hierarchy/interfaces/hierarchy-type.interface'

export interface IApiHierarchy {
  getPicklists(): Promise<IPicklist[]>
  getHierarchicalPicklists(): Promise<IPicklist[]>
  getItemById<T>(hierarchyItemType: IHierarchyType, id: string): Promise<T | void>
  getItemsBySearch<T>(
    hierarchyItemType: IHierarchyType,
    searchParams: ISearchParams
  ): Promise<T | void>
  createItem<T>(hierarchyItemType: IHierarchyType, itemObject: any): Promise<T | void>
  updateItem<T>(hierarchyItemType: IHierarchyType, itemObject: any, id: string): Promise<T | void>
  disablingStatesItem<T>(hierarchyItemType: IHierarchyType, id: string): Promise<T | void>
}
