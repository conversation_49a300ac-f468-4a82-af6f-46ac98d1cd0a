export interface RoleResponse {
  id: number
  name: string
  needSellingConfiguration: boolean
  application: {
    id: number
    name: string
    imageUrl: string
    appUrl: string
    roles: any[]
    createdDate: string
    createdById: string
    lastModifiedDate: string
    lastModifiedById: string
    deactivationDate: string | null
    rowVersion: string
  }
  description: string
  functions: FunctionItem[]
  createdDate: string
  createdById: string
  lastModifiedDate: string
  lastModifiedById: string
  deactivationDate: string | null
  rowVersion: string
}

export interface FunctionItem {
  id: string
  name: string
  roles: any[]
  createdDate: string
  createdById: string
  lastModifiedDate: string
  lastModifiedById: string
  deactivationDate: string | null
  rowVersion: string
}
