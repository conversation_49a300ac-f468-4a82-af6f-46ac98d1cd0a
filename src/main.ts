import './assets/styles/main.css'
import '../node_modules/parlem-webcomponents-common/dist/parlem-webcomponents-common.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import i18nInstance from '@/i18n'
import ToastPlugin from 'vue-toast-notification'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { library } from '@fortawesome/fontawesome-svg-core'

// Importar només les icones que utilitzem (free-solid-svg-icons)
import {
  faHome,
  faRocket,
  faPersonWalkingDashedLineArrowRight,
  faLocationCrosshairs,
  faUserTie,
  faUserShield,
  faUsersViewfinder,
  faPeopleGroup,
  faPlus,
  faMinus,
  faTags,
  faChevronUp,
  faChevronDown,
  faTrash,
  faRotate,
  faCircleInfo,
  faTriangleExclamation,
  faMagnifyingGlass,
  faCircleCheck,
  faPencil,
  faFlagCheckered,
  faClone,
  faTrashCan,
  faSpinner,
  faUser
} from '@fortawesome/free-solid-svg-icons'

// Importar només les icones que utilitzem (free-regular-svg-icons)
import { faFaceSmile, faCircleDot } from '@fortawesome/free-regular-svg-icons'

const app = createApp(App)

// Afegir només les icones necessàries a la llibreria
library.add(
  // Solid icons
  faHome,
  faRocket,
  faPersonWalkingDashedLineArrowRight,
  faLocationCrosshairs,
  faMagnifyingGlass,
  faUserTie,
  faUserShield,
  faUsersViewfinder,
  faPeopleGroup,
  faPlus,
  faChevronUp,
  faChevronDown,
  faTrash,
  faRotate,
  faCircleInfo,
  faTriangleExclamation,
  faCircleCheck,
  faPencil,
  faFlagCheckered,
  faClone,
  faTrashCan,
  faTags,
  faSpinner,
  faUser,
  faMinus,
  // Regular icons
  faFaceSmile,
  faCircleDot
)
app
  .use(createPinia())
  .use(router)
  .use(i18nInstance)
  .use(ToastPlugin)
  .component('font-awesome-icon', FontAwesomeIcon)

app.mount('#app')
