@import './base.css';
@import 'tailwindcss';

@theme {
  --color-gray-light: #e4e4e4;
  --color-gray-super-light: #f1f1f1;
  --color-white: #ffffff;
  --color-black: #000000;
  --color-error: #9b1c1c;
  --color-error-light: #ffcccc;
  --color-warning: #c27803;
  --color-success: #0e9f6e;
  --color-success-light: #cce8d9;
  --color-info: #6b7280;
  --color-gray: #c1c1c1;
  --color-dark: #181818;
  --color-dark-light: #282828;
  --color-dark-hover: #484848;
  --color-dark-gray-light: #5b5f62;
  --color-dark-gray: #3c4043;
  --color-gray-dark: #919191;
  --color-background: #ffffff;
}

:root {
  --color-primary: 252 189 41;
  --color-secondary: 0 0 0;
  --color-primary-light: 255 242 210;
}

body {
  width: 100%;
  min-height: 100vh;
  background-color: var(--color-background);
  transition:
    color 0.5s,
    background-color 0.5s;
  font-family: 'Raleway', 'Roboto', 'Helvetica Neue', sans-serif;
  font-feature-settings: 'lnum' 1;
  overflow-x: hidden;
}

#app {
  margin: 0 auto;
  padding: 1.2rem 2rem;
  font-weight: normal;
  height: 100vh;
  position: relative;
}

#app::-webkit-scrollbar {
  width: 12px; /* width of the entire scrollbar */
}

#app::-webkit-scrollbar-track {
  background: transparent; /* color of the tracking area */
}

#app::-webkit-scrollbar-thumb {
  background-color: blue; /* color of the scroll thumb */
  border-radius: 20px; /* roundness of the scroll thumb */
  border: 3px solid transparent; /* creates padding around scroll thumb */
}

.no-scrollbar {
  overflow-y: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.no-scrollbar::-webkit-scrollbar {
  width: 0;
  background: transparent;
}
