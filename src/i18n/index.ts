import { createI18n } from 'vue-i18n'

import ca from '../i18n/locales/ca.json'
import es from '../i18n/locales/es.json'
import en from '../i18n/locales/en.json'

const instance = createI18n({
  locale: import.meta.env.VITE_DEFAULT_LOCALE,
  fallbackLocale: import.meta.env.VITE_FALLBACK_LOCALE,
  legacy: false,
  globalInjection: true,
  sync: false, //Si esta en true es sincronitzarien les diverses llibreries dels idiomes i si un nom coincideix es sobrescriuria.
  messages: {
    en,
    es,
    ca
  }
})

export default instance

export const i18n = instance.global
