import type { IFirstFilter } from '@/components/search/interfaces/firstFilter.interface'

const searchFilterOptions: IFirstFilter[] = [
  {
    label: 'Usuari AD',
    value: 'userAD',
    key: 'userAD',
    type: 'text'
  },
  {
    label: 'ID objecte',
    value: 'objectId',
    key: 'objectId',
    type: 'text'
  },
  { label: 'Nom', value: 'name', key: 'name', type: 'text' },
  { label: 'Cognoms', value: 'surname', key: 'surname', type: 'text' },
  {
    label: 'Companyia',
    value: 'company',
    key: 'company',
    type: 'selector',
    options: []
  },
  {
    label: 'Departament',
    value: 'department',
    key: 'department',
    type: 'selector',
    options: []
  },
  {
    label: 'Estat',
    value: 'active',
    key: 'active',
    type: 'selector',
    options: [
      { label: 'Actiu', value: true },
      { label: 'No Actiu', value: false }
    ]
  },
  {
    label: 'Id',
    value: 'id',
    key: 'id',
    type: 'text'
  },
  {
    label: 'Codi Intern',
    value: 'internalCode',
    key: 'internalCode',
    type: 'text'
  },
  {
    label: 'Ciutat',
    value: 'city',
    key: 'city',
    type: 'text'
  },
  {
    label: 'Codi Postal',
    value: 'postalCode',
    key: 'postalCode',
    type: 'text'
  },
  {
    label: 'Codi Postal',
    value: 'province',
    key: 'province',
    type: 'selector',
    options: []
  },
  {
    label: 'Nº document',
    value: 'documentNumber',
    key: 'documentNumber',
    type: 'text'
  },
  {
    label: 'Correu electrònic',
    value: 'email',
    key: 'email',
    type: 'text'
  },
  {
    label: 'Nom aplicació',
    value: 'applicationName',
    key: 'applicationName',
    type: 'text'
  },
  {
    label: 'És rol comercial?',
    value: 'needSellingConfiguration',
    key: 'needSellingConfiguration',
    type: 'selector',
    options: [
      { label: 'Si', value: true },
      { label: 'No ', value: false }
    ]
  },
  {
    label: 'Id funció',
    value: 'functionId',
    key: 'functionId',
    type: 'text'
  },
  {
    label: 'Responsable directe',
    value: 'directResponsible',
    key: 'directResponsible',
    type: 'text'
  },
  {
    label: 'Responsable superior',
    value: 'treeResponsible',
    key: 'treeResponsible',
    type: 'text'
  },
  {
    label: 'Nom del rol',
    value: 'roleName',
    key: 'roleName',
    type: 'text'
  },
  {
    label: 'Id Dealer',
    value: 'dealerId',
    key: 'dealerId',
    type: 'text'
  },
  {
    label: 'Id Subdealer',
    value: 'subDealerId',
    key: 'subDealerId',
    type: 'text'
  },
  {
    label: 'Canal',
    value: 'channel',
    key: 'channel',
    type: 'selector',
    options: []
  },
  {
    label: 'Nom legal',
    value: 'legalName',
    key: 'legalName',
    type: 'text'
  },
  {
    label: 'Codi Comercial',
    value: 'comercialCode',
    key: 'comercialCode',
    type: 'text'
  },
  {
    label: 'Tipus de Dealer',
    value: 'dealerType',
    key: 'dealerType',
    type: 'selector',
    options: []
  }
]

export default searchFilterOptions
