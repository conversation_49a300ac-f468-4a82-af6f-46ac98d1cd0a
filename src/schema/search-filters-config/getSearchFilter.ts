import type { IPicklist } from '@/services/api/hierarchy/interfaces'
import searchFilterOptions from './searchFilterOptions'
import { useHierarchyStore } from '@/stores/hierarchy'
import type { IFirstFilter } from '@/components/search/interfaces/firstFilter.interface'

const hierarchyStore = useHierarchyStore()

export default function getSearchFilter(searchFilterKeys: string[]): IFirstFilter[] | null {
  const searchFilter = searchFilterKeys
    .map((key: string) => {
      const searchFilterOption = searchFilterOptions.find((option) => option.key === key)
      if (!searchFilterOption) return null

      if (hierarchyStore.getPicklists.length && searchFilterOption.type === 'selector') {
        const optionSelected: IPicklist | undefined = hierarchyStore.getPicklists.find(
          (option: IPicklist) =>
            option.name.toLowerCase() ===
            `${searchFilterOption.key ? searchFilterOption.key : searchFilterOption.value}`.toLowerCase()
        )

        if (optionSelected !== undefined) {
          searchFilterOption.options = optionSelected.values
        }
      }

      const filter = { ...searchFilterOption }

      return filter
    })
    .filter(Boolean)

  return searchFilter.filter((filter): filter is IFirstFilter => filter !== null)
}
