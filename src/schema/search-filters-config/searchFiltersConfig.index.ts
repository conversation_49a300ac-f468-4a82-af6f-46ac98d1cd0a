import userSearchFilterKeys from '@/modules/users/schema/userSearchFilters'
import type { ISearchFiltersConfig } from './interfaces/search-filters-config.interface'
import rolesSearchFilterKeys from '@/modules/roles/schema/rolesSearchFilters'
import applicationsSearchFilterKeys from '@/modules/applications/schema/applicationsSearchFilters'
import functionsSearchFilterKeys from '@/modules/functions/schema/functionsSearchFilters'
import profilesSearchFilterKeys from '@/modules/profiles/schema/profilesSearchFilters'
import dealersSearchFilterKeys from '@/modules/dealers/schema/dealersSearchFilters'
import subdealersSearchFilterKeys from '@/modules/subdealers/schema/subdealersSearchFilters'

const searchFilterConfig: ISearchFiltersConfig = {
  //hierarchyItemKey
  users: userSearchFilterKeys,
  roles: rolesSearchFilterKeys,
  applications: applicationsSearchFilterKeys,
  functions: functionsSearchFilterKeys,
  profiles: profilesSearchFilterKeys,
  dealers: dealersSearchFilterKeys,
  subdealers: subdealersSearchFilterKeys
}

export default searchFilterConfig
