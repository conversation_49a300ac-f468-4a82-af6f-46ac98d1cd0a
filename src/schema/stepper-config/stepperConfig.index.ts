import type { IStepperConfig } from './interfaces/stepper-config.interface'
import userEditStepper from '@/modules/users/schema/stepper/userEditStepper'
import userCreateStepper from '@/modules/users/schema/stepper/userCreateStepper'
import roleCreateStepper from '@/modules/roles/schema/stepper/roleCreateStepper'
import roleEditStepper from '@/modules/roles/schema/stepper/roleEditStepper'
import applicationCreateStepper from '@/modules/applications/schema/stepper/applicationCreateStepper'
import applicationEditStepper from '@/modules/applications/schema/stepper/applicationEditStepper'
import functionCreateStepper from '@/modules/functions/schema/stepper/functionCreateStepper'
import functionEditStepper from '@/modules/functions/schema/stepper/functionEditStepper'
import profileCreateStepper from '@/modules/profiles/schema/stepper/profileCreateStepper'
import profileEditStepper from '@/modules/profiles/schema/stepper/profileEditStepper'
import dealerCreateStepper from '@/modules/dealers/schema/stepper/dealerCreateStepper'
import dealerEditStepper from '@/modules/dealers/schema/stepper/dealerEditStepper'
import subdealerCreateStepper from '@/modules/subdealers/schema/stepper/subdealerCreateStepper'
import subdealerEditStepper from '@/modules/subdealers/schema/stepper/subdealerEditStepper'

const stepperConfig: IStepperConfig = {
  //hierarchyItemKey
  users: {
    //action
    create: userCreateStepper,
    edit: userEditStepper
  },
  roles: {
    create: roleCreateStepper,
    edit: roleEditStepper
  },
  applications: {
    create: applicationCreateStepper,
    edit: applicationEditStepper
  },
  functions: {
    create: functionCreateStepper,
    edit: functionEditStepper
  },
  profiles: {
    create: profileCreateStepper,
    edit: profileEditStepper
  },
  dealers: {
    create: dealerCreateStepper,
    edit: dealerEditStepper
  },
  subdealers: {
    create: subdealerCreateStepper,
    edit: subdealerEditStepper
  }
}

export default stepperConfig
