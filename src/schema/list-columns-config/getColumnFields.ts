import columsFieldOptions from './columnsOptions'
import type { IColumn } from '@/components/list/interfaces/column.interface'

export default function getColumnFields(columFieldKeys: string[]): IColumn[] {
  const columnFields = columFieldKeys
    .map((key: string) => {
      const fieldOption = columsFieldOptions.find((option) => option.key === key)
      if (!fieldOption) return null

      const column = { ...fieldOption }

      return column
    })
    .filter((column): column is IColumn => column !== null)

  return columnFields || []
}
