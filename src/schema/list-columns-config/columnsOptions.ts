import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OpenChildren<PERSON>ell<PERSON><PERSON><PERSON>,
  ActionsCellRenderer,
  ChildCell<PERSON>enderer,
  DeepValueCellRenderer,
  BooleanCell<PERSON>enderer,
  PicklistValueCellRenderer
} from '@/components/list/cell-renderers/index'
import type { IColumn } from '@/components/list/interfaces/column.interface'

///IMPORTANT: La paraula "children" està reservada per crear els llistats interiors a un llistat a un tipus d'element. S'Utilitza per poder saber què s'ha de mostrar.

const columsFieldOptions: IColumn[] = [
  //Generic

  {
    field: 'id',
    key: 'childId',
    headerName: '',
    minWidth: 50,
    maxWidth: 50,
    cellRenderer: Child<PERSON><PERSON><PERSON><PERSON><PERSON>,
    editable: false,
    filter: false,
    sortable: false
  },
  { field: 'id', key: 'visibleId', minWidth: 50, headerName: 'Id', hide: false },
  { field: 'id', key: 'id', headerName: 'Id', hide: true },
  {
    field: 'name',
    key: 'name',
    minWidth: 130,
    headerName: 'Nom',
    checkboxSelection: false,
    headerCheckboxSelection: false
  },
  {
    field: 'surname',
    key: 'surname',
    minWidth: 130,
    headerName: 'Cognoms'
  },
  {
    field: 'company',
    key: 'company',
    minWidth: 100,
    headerName: 'Companyia'
  },
  {
    field: 'trademark',
    key: 'trademark',
    minWidth: 100,
    headerName: 'Marca'
  },
  {
    field: 'channel',
    key: 'channel',
    minWidth: 100,
    headerName: 'Canal'
  },
  {
    field: 'responsible',
    key: 'responsible',
    minWidth: 100,
    headerName: 'Responsable'
  },
  {
    field: 'role',
    key: 'role',
    minWidth: 100,
    headerName: 'Rol'
  },
  {
    field: 'departments',
    key: 'departments',
    minWidth: 140,
    headerName: 'Departaments',
    cellRenderer: PicklistValueCellRenderer,
    filter: false,
    cellRendererParams: {
      picklistName: 'SecGroups'
    }
  },
  {
    field: 'userAD',
    key: 'userAD',
    minWidth: 100,
    headerName: 'Usuari Azure'
  },
  {
    field: 'user.userAD',
    key: 'user',
    minWidth: 100,
    headerName: 'Usuari Azure',
    cellRenderer: DeepValueCellRenderer
  },
  {
    field: 'emailAddress',
    key: 'emailAddress',
    minWidth: 100,
    headerName: 'Correu electrònic'
  },
  {
    field: 'email',
    key: 'email',
    minWidth: 100,
    headerName: 'Correu electrònic'
  },
  {
    field: 'application.name',
    key: 'applicationName',
    minWidth: 100,
    headerName: 'Aplicació',
    cellRenderer: DeepValueCellRenderer
  },
  {
    field: 'needSellingConfiguration',
    key: 'needSellingConfiguration',
    headerName: 'És rol comercial?',
    cellRenderer: BooleanCellRenderer
  },
  {
    field: 'deactivationDate',
    key: 'status',
    headerName: 'Estat',
    maxWidth: 180,
    minWidth: 85,
    cellRenderer: StatusCellRenderer,
    filter: false,
    sortable: false,
    cellStyle: { display: 'flex', alignItems: 'center' }
  },
  /*   {
    field: 'deactivationDate',
    key: 'deactivationDate',
    headerName: 'Data de desactivació',
    minWidth: 85,
    filter: false,
    sortable: false,
    valueFormatter: (params: any) => {
      return params.value && params.value !== '-'
        ? new Date(params.value).toLocaleDateString('ca-ES')
        : '-'
    }
  }, */
  {
    field: 'dealer',
    key: 'dealerName',
    headerName: 'Nom Dealer',
    minWidth: 100
  },
  {
    field: 'dealerType',
    key: 'dealerType',
    headerName: 'Tipus de dealer',
    minWidth: 85
  },
  {
    field: 'dealer.name',
    key: 'subDealerDealerName',
    minWidth: 100,
    headerName: 'Dealer',
    cellRenderer: DeepValueCellRenderer
  },
  {
    field: 'dealer.comercialCode',
    key: 'subDealerDealerCode',
    minWidth: 100,
    headerName: 'Codi Dealer',
    cellRenderer: DeepValueCellRenderer
  },
  {
    field: 'documentNumber',
    key: 'documentNumber',
    headerName: 'Nº document',
    minWidth: 85
  },
  {
    field: 'contactName',
    key: 'contactName',
    headerName: 'Nom contacte',
    minWidth: 100
  },
  {
    field: 'contactPhone',
    key: 'contactPhone',
    headerName: 'Telèfon',
    minWidth: 100
  },
  {
    field: 'comercialCode',
    key: 'comercialCode',
    headerName: 'Codi Comercial',
    minWidth: 75
  },
  {
    field: 'internalCode',
    key: 'internalCode',
    headerName: 'Codi intern',
    minWidth: 75
  },
  {
    field: 'actions',
    key: 'actions',
    headerName: 'Accions',
    cellRenderer: ActionsCellRenderer,
    editable: false,
    filter: false,
    sortable: false,
    minWidth: 100,
    maxWidth: 150,
    cellStyle: { display: 'flex', alignItems: 'center' }
  },
  { field: 'rowVersion', key: 'rowVersion', headerName: '', hide: true },
  {
    field: 'actions',
    key: 'childActions',
    headerName: 'Accions',
    cellRenderer: ActionsCellRenderer,
    editable: false,
    filter: false,
    sortable: false,
    minWidth: 155,
    maxWidth: 155,
    cellStyle: { display: 'flex', alignItems: 'center' }
  },

  //Children
  {
    field: 'id',
    key: 'roleChildren',
    headerName: '',
    cellRenderer: OpenChildrenCellRenderer,
    editable: false,
    minWidth: 50,
    maxWidth: 50,
    filter: false,
    sortable: false,
    cellRendererParams: {
      childrenType: ['functions']
    }
  },
  {
    field: 'id',
    key: 'profileChildren',
    headerName: '',
    cellRenderer: OpenChildrenCellRenderer,
    editable: false,
    minWidth: 50,
    maxWidth: 50,
    filter: false,
    sortable: false,
    cellRendererParams: {
      childrenType: ['catalogs']
    }
  }
]

export default columsFieldOptions
