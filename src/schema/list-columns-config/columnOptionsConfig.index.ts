import type { IColumnFieldsConfig } from './interfaces/column-fields-config.interface'
import userListColumnsFieldKeys from '@/modules/users/schema/userListColumns'
import rolesListColumnsFieldKeys from '@/modules/roles/schema/rolesListColumns'
import applicationsListColumnsFieldKeys from '@/modules/applications/schema/applicationsListColumns'
import functionsListColumnsFieldKeys from '@/modules/functions/schema/functionsListColumns'
import profilesListColumnsFieldKeys from '@/modules/profiles/schema/profilesListColumns'
import dealersListColumnsFieldKeys from '@/modules/dealers/schema/dealersListColumns'
import subdealersListColumnsFieldKeys from '@/modules/subdealers/schema/subdealersListColumns'

const columnOptionsConfig: IColumnFieldsConfig = {
  //hierarchyItemKey
  users: userListColumnsFieldKeys,
  roles: rolesListColumnsFieldKeys,
  applications: applicationsListColumnsFieldKeys,
  functions: functionsListColumnsFieldKeys,
  profiles: profilesListColumnsFieldKeys,
  dealers: dealersListColumnsFieldKeys,
  subdealers: subdealersListColumnsFieldKeys
}

export default columnOptionsConfig
