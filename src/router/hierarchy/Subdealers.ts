import type { RouteRecordRaw } from 'vue-router'
const ListView = () => import('@/views/ListView.vue')
const StepWorkflowView = () => import('@/views/StepWorkflowView.vue')

const hierarchyItemType = 'subdealers'

const subdealersRoutes: RouteRecordRaw[] = [
  {
    path: 'subdealers',
    name: 'subdealers',
    meta: {
      icon: 'fa-people-group',
      breadcrumb: {
        title: 'Comercial Subdealers'
      }
    },
    children: [
      {
        path: '',
        name: 'subdealers-list',
        props: {
          hierarchyItemType,
          hierarchyItemKey: 'subdealers'
        },
        component: ListView,
        meta: {
          breadcrumb: {
            title: 'Llista de subdealers'
          },
          componentName: 'ListView'
        },
        children: []
      },
      {
        path: '',
        name: 'subdealers-create',
        component: StepWorkflowView,
        props: {
          hierarchyItemType,
          hierarchyItemKey: hierarchyItemType
        },
        meta: {
          breadcrumb: {
            title: 'Crear subdealer'
          }
        },
        children: [
          {
            path: 'general',
            name: `${hierarchyItemType}-create-step1`,
            meta: {
              title: 'Omple les dades generals',
              breadcrumb: {
                title: 'Dades generals'
              },
              componentName: 'FormView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'summary',
            name: `${hierarchyItemType}-create-step2`,
            meta: {
              title: 'Resum dades subdealer',
              breadcrumb: {
                title: 'Resum de dades'
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'success',
            name: `${hierarchyItemType}-create-step3`,
            meta: {
              title: '',
              breadcrumb: {
                title: ''
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          }
        ]
      },
      {
        path: '',
        name: 'subdealers-edit',
        component: StepWorkflowView,
        props: {
          hierarchyItemType,
          hierarchyItemKey: hierarchyItemType
        },
        meta: {
          breadcrumb: {
            title: 'Editar subdealer'
          }
        },
        children: [
          {
            path: 'general/:id',
            name: `${hierarchyItemType}-edit-step1`,
            meta: {
              title: 'Omple les dades generals',
              breadcrumb: {
                title: 'Dades generals'
              },
              componentName: 'FormView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'summary',
            name: `${hierarchyItemType}-edit-step2`,
            meta: {
              title: 'Resum dades subdealer',
              breadcrumb: {
                title: 'Resum de dades'
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'success',
            name: `${hierarchyItemType}-edit-step3`,
            meta: {
              title: '',
              breadcrumb: {
                title: ''
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          }
        ]
      },
      {
        path: 'detail/:id',
        name: 'subdealers-detail',
        component: () => import('@/modules/subdealers/components/SubdealerDetailView.vue'), // Asume que crearás este componente
        props: {
          hierarchyItemType,
          hierarchyItemKey: hierarchyItemType
        },
        meta: {
          breadcrumb: {
            title: 'Detall SubDealer'
          },
          componentName: 'SubdealerDetailView'
        }
      }
    ]
  }
]
export default subdealersRoutes
