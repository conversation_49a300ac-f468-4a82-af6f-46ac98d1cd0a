import type { RouteRecordRaw } from 'vue-router'
const ListView = () => import('@/views/ListView.vue')
const StepWorkflowView = () => import('@/views/StepWorkflowView.vue')

const hierarchyItemType = 'roles'

const rolesRoutes: RouteRecordRaw[] = [
  {
    path: 'roles',
    name: 'roles',
    meta: {
      icon: 'fa-tags',
      breadcrumb: {
        title: 'Rols'
      }
    },
    children: [
      {
        path: '',
        name: 'roles-list',
        props: {
          hierarchyItemType,
          hierarchyItemKey: hierarchyItemType
        },
        component: ListView,
        meta: {
          breadcrumb: {
            title: 'Llista de rols'
          },
          componentName: 'ListView'
        },
        children: []
      },
      {
        path: '',
        name: 'roles-create',
        component: StepWorkflowView,
        props: {
          hierarchyItemType,
          hierarchyItemKey: hierarchyItemType
        },
        meta: {
          breadcrumb: {
            title: 'Crear rol'
          }
        },
        children: [
          {
            path: 'general',
            name: `${hierarchyItemType}-create-step1`,
            /*             component: FormView,
             */ meta: {
              title: 'Omple les dades generals',
              breadcrumb: {
                title: 'Dades generals'
              },
              componentName: 'FormView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'application',
            name: `${hierarchyItemType}-create-step2`,
            /*             component: FormView,
             */ meta: {
              title: 'Selecciona una aplicació',
              breadcrumb: {
                title: 'Aplicació'
              },
              componentName: 'FormView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'functions',
            name: `${hierarchyItemType}-create-step3`,
            /*             component: FormView,
             */ meta: {
              title: 'Selecciona les funcions del rol',
              breadcrumb: {
                title: 'Funcions'
              },
              componentName: 'FormView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'summary',
            name: `${hierarchyItemType}-create-step4`,
            /*             component: FormView,
             */ meta: {
              title: 'Resum dades usuari',
              breadcrumb: {
                title: 'Resum de dades'
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'success',
            name: `${hierarchyItemType}-create-step5`,
            /*             component: FormView,
             */ meta: {
              title: '',
              breadcrumb: {
                title: ''
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          }
        ]
      },
      {
        path: '',
        name: 'users-edit',
        component: StepWorkflowView,
        props: {
          hierarchyItemType,
          hierarchyItemKey: hierarchyItemType
        },
        meta: {
          breadcrumb: {
            title: 'Editar usuari'
          }
        },
        children: [
          {
            path: 'general/:id',
            name: `${hierarchyItemType}-edit-step1`,
            /*             component: FormView,
             */ meta: {
              title: 'Omple les dades generals',
              breadcrumb: {
                title: 'Dades generals'
              },
              componentName: 'FormView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'application/:id',
            name: `${hierarchyItemType}-edit-step2`,
            /*             component: FormView,
             */ meta: {
              title: 'Selecciona una aplicació',
              breadcrumb: {
                title: 'Aplicació'
              },
              componentName: 'FormView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'functions/:id',
            name: `${hierarchyItemType}-edit-step3`,
            /*             component: FormView,
             */ meta: {
              title: 'Selecciona les funcions del rol',
              breadcrumb: {
                title: 'Funcions'
              },
              componentName: 'FormView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'summary/:id',
            name: `${hierarchyItemType}-edit-step4`,
            /*             component: FormView,
             */ meta: {
              title: 'Resum dades usuari',
              breadcrumb: {
                title: 'Resum de dades'
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'success',
            name: `${hierarchyItemType}-edit-step5`,
            /*             component: FormView,
             */ meta: {
              title: '',
              breadcrumb: {
                title: ''
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          }
        ]
      },
      {
        path: 'detail/:id',
        name: 'roles-detail',
        component: () => import('@/modules/roles/components/RolDetailView.vue'), // Asume que crearás este componente
        props: {
          hierarchyItemType,
          hierarchyItemKey: hierarchyItemType
        },
        meta: {
          breadcrumb: {
            title: 'Detall rol'
          },
          componentName: 'RolDetailView'
        }
      }
    ]
  }
]
export default rolesRoutes
