import type { RouteRecordRaw } from 'vue-router'
const ListView = () => import('@/views/ListView.vue')
const StepWorkflowView = () => import('@/views/StepWorkflowView.vue')

const hierarchyItemType = 'profiles'

const profilesRoutes: RouteRecordRaw[] = [
  {
    path: 'profiles',
    name: 'profiles',
    meta: {
      icon: 'fa-user-shield',
      breadcrumb: {
        title: 'Perfils'
      }
    },
    children: [
      {
        path: '',
        name: 'profiles-list',
        props: {
          hierarchyItemType,
          hierarchyItemKey: 'profiles'
        },
        component: ListView,
        meta: {
          breadcrumb: {
            title: 'Llista de perfils'
          },
          componentName: 'ListView'
        },
        children: []
      },
      {
        path: '',
        name: 'profiles-create',
        component: StepWorkflowView,
        props: {
          hierarchyItemType,
          hierarchyItemKey: hierarchyItemType
        },
        meta: {
          breadcrumb: {
            title: '<PERSON>rea<PERSON> perfil'
          }
        },
        children: [
          {
            path: 'general',
            name: `${hierarchyItemType}-create-step1`,
            meta: {
              title: 'Omple les dades generals',
              breadcrumb: {
                title: 'Dades generals'
              },
              componentName: 'FormView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'summary',
            name: `${hierarchyItemType}-create-step2`,
            meta: {
              title: 'Resum dades perfil',
              breadcrumb: {
                title: 'Resum de dades'
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'success',
            name: `${hierarchyItemType}-create-step3`,
            meta: {
              title: '',
              breadcrumb: {
                title: ''
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          }
        ]
      },
      {
        path: '',
        name: 'profiles-edit',
        component: StepWorkflowView,
        props: {
          hierarchyItemType,
          hierarchyItemKey: hierarchyItemType
        },
        meta: {
          breadcrumb: {
            title: 'Editar perfil'
          }
        },
        children: [
          {
            path: 'general/:id',
            name: `${hierarchyItemType}-edit-step1`,
            meta: {
              title: 'Omple les dades generals',
              breadcrumb: {
                title: 'Dades generals'
              },
              componentName: 'FormView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'summary',
            name: `${hierarchyItemType}-edit-step2`,
            meta: {
              title: 'Resum dades perfil',
              breadcrumb: {
                title: 'Resum de dades'
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'success',
            name: `${hierarchyItemType}-edit-step3`,
            meta: {
              title: '',
              breadcrumb: {
                title: ''
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          }
        ]
      },
      {
        path: 'detail/:id',
        name: 'profiles-detail',
        component: () => import('@/modules/profiles/components/ProfileDetailView.vue'), // Asume que crearás este componente
        props: {
          hierarchyItemType,
          hierarchyItemKey: hierarchyItemType
        },
        meta: {
          breadcrumb: {
            title: 'Detall Perfil'
          },
          componentName: 'ProfileDetailView'
        }
      }
    ]
  }
]
export default profilesRoutes
