import usersRoutes from './Users'
import homeRoutes from './Home'

import type { RouteRecordRaw } from 'vue-router'
import rolesRoutes from './Roles'
import applicationsRoutes from './Applications'
import functionsRoutes from './Functions'
import profilesRoutes from './Profiles'
import dealersRoutes from './Dealers'
import subdealersRoutes from './Subdealers'

const HierarchyRoutes: RouteRecordRaw[] = [
  ...homeRoutes,
  {
    path: '/',
    component: () => import('@/views/BaseView.vue'),
    children: [
      ...usersRoutes,
      ...rolesRoutes,
      ...applicationsRoutes,
      ...functionsRoutes,
      ...profilesRoutes,
      ...dealersRoutes,
      ...subdealersRoutes
    ]
  }
]
export default HierarchyRoutes
