import type { RouteRecordRaw } from 'vue-router'
const ListView = () => import('@/views/ListView.vue')
const StepWorkflowView = () => import('@/views/StepWorkflowView.vue')

const hierarchyItemType = 'functions'

const functionsRoutes: RouteRecordRaw[] = [
  {
    path: 'functions',
    name: 'functions',
    meta: {
      icon: 'fa-location-crosshairs',
      breadcrumb: {
        title: 'Funcions'
      }
    },
    children: [
      {
        path: '',
        name: 'functions-list',
        props: {
          hierarchyItemType,
          hierarchyItemKey: 'functions'
        },
        component: ListView,
        meta: {
          breadcrumb: {
            title: 'Llista de funcions'
          },
          componentName: 'ListView'
        },
        children: []
      },
      {
        path: '',
        name: 'functions-create',
        component: StepWorkflowView,
        props: {
          hierarchyItemType,
          hierarchyItemKey: hierarchyItemType
        },
        meta: {
          breadcrumb: {
            title: '<PERSON><PERSON><PERSON> funci<PERSON>'
          }
        },
        children: [
          {
            path: 'general',
            name: `${hierarchyItemType}-create-step1`,
            meta: {
              title: 'Omple les dades generals',
              breadcrumb: {
                title: 'Dades generals'
              },
              componentName: 'FormView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'summary',
            name: `${hierarchyItemType}-create-step2`,
            meta: {
              title: 'Resum dades funció',
              breadcrumb: {
                title: 'Resum de dades'
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'success',
            name: `${hierarchyItemType}-create-step3`,
            meta: {
              title: '',
              breadcrumb: {
                title: ''
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          }
        ]
      },
      {
        path: '',
        name: 'functions-edit',
        component: StepWorkflowView,
        props: {
          hierarchyItemType,
          hierarchyItemKey: hierarchyItemType
        },
        meta: {
          breadcrumb: {
            title: 'Editar funció'
          }
        },
        children: [
          {
            path: 'general/:id',
            name: `${hierarchyItemType}-edit-step1`,
            meta: {
              title: 'Omple les dades generals',
              breadcrumb: {
                title: 'Dades generals'
              },
              componentName: 'FormView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'summary',
            name: `${hierarchyItemType}-edit-step2`,
            meta: {
              title: 'Resum dades funció',
              breadcrumb: {
                title: 'Resum de dades'
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'success',
            name: `${hierarchyItemType}-edit-step3`,
            meta: {
              title: '',
              breadcrumb: {
                title: ''
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          }
        ]
      },
      {
        path: 'detail/:id',
        name: 'functions-detail',
        component: () => import('@/modules/functions/components/FunctionDetailView.vue'), // Asume que crearás este componente
        props: {
          hierarchyItemType,
          hierarchyItemKey: hierarchyItemType
        },
        meta: {
          breadcrumb: {
            title: 'Detall funció'
          },
          componentName: 'FunctionDetailView'
        }
      }
    ]
  }
]
export default functionsRoutes
