import type { RouteRecordRaw } from 'vue-router'
const ListView = () => import('@/views/ListView.vue')
const StepWorkflowView = () => import('@/views/StepWorkflowView.vue')

const hierarchyItemType = 'users'

const usersRoutes: RouteRecordRaw[] = [
  {
    path: 'users',
    name: 'users',
    meta: {
      icon: 'fa-tags',
      breadcrumb: {
        title: 'Usuaris'
      }
    },
    children: [
      {
        path: '',
        name: 'users-list',
        props: {
          hierarchyItemType,
          hierarchyItemKey: 'users'
        },
        component: ListView,
        meta: {
          breadcrumb: {
            title: "Llista d'usuaris"
          },
          componentName: 'ListView'
        },
        children: []
      },
      {
        path: '',
        name: 'users-create',
        component: StepWorkflowView,
        props: {
          hierarchyItemType,
          hierarchyItemKey: hierarchyItemType
        },
        meta: {
          breadcrumb: {
            title: '<PERSON>rea<PERSON> usuari'
          }
        },
        children: [
          {
            path: 'general',
            name: `${hierarchyItemType}-create-step1`,
            /*             component: FormView,
             */ meta: {
              title: 'Omple les dades generals',
              breadcrumb: {
                title: 'Dades generals'
              },
              componentName: 'FormView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'summary',
            name: `${hierarchyItemType}-create-step2`,
            /*             component: FormView,
             */ meta: {
              title: 'Resum dades usuari',
              breadcrumb: {
                title: 'Resum de dades'
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'success',
            name: `${hierarchyItemType}-create-step3`,
            /*             component: FormView,
             */ meta: {
              title: '',
              breadcrumb: {
                title: ''
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          }
        ]
      },
      {
        path: '',
        name: 'users-edit',
        component: StepWorkflowView,
        props: {
          hierarchyItemType,
          hierarchyItemKey: hierarchyItemType
        },
        meta: {
          breadcrumb: {
            title: 'Editar usuari'
          }
        },
        children: [
          {
            path: 'general/:id',
            name: `${hierarchyItemType}-edit-step1`,
            /*             component: FormView,
             */ meta: {
              title: 'Omple les dades generals',
              breadcrumb: {
                title: 'Dades generals'
              },
              componentName: 'FormView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'summary',
            name: `${hierarchyItemType}-edit-step2`,
            /*             component: FormView,
             */ meta: {
              title: 'Resum dades usuari',
              breadcrumb: {
                title: 'Resum de dades'
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          },
          {
            path: 'success',
            name: `${hierarchyItemType}-edit-step3`,
            /*             component: FormView,
             */ meta: {
              title: '',
              breadcrumb: {
                title: ''
              },
              componentName: 'SummaryView'
            },
            props: {
              hierarchyItemType,
              hierarchyItemKey: hierarchyItemType
            },
            children: []
          }
        ]
      },
      {
        path: 'detail/:id',
        name: 'users-detail',
        component: () => import('@/modules/users/components/UserDetailView.vue'), // Asume que crearás este componente
        props: {
          hierarchyItemType,
          hierarchyItemKey: hierarchyItemType
        },
        meta: {
          breadcrumb: {
            title: 'Detall Usuari'
          },
          componentName: 'UserDetailView'
        }
      }
    ]
  }
]
export default usersRoutes
