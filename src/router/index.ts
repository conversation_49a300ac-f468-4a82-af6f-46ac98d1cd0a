import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import checkUserCredentials from '@/utils/user-credentials/userCredentials'
import HierarchyRoutes from './hierarchy'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [...HierarchyRoutes]
})

router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  await checkUserCredentials()
  //add before route to meta route
  /*   to.meta.from = from
  to.meta.component = to.matched[to.matched.length - 1]?.components?.default?.name

  if (authStore.authenticationError) {
    if (to.path !== '/unauthorized') {
      return { name: 'Not Authorized' }
    }
  } else {
    if (to.path === '/unauthorized') {
      return { name: 'Home' }
    }
  } */
  next()
})

export default router
