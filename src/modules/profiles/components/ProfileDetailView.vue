<template>
  <div class="h-full">
    <h2 class="text-primary">CARD</h2>
    <CardDetail
      icon="user-shield"
      title="DETALL PERFIL"
      :name="titleEntity"
      :description="descriptionEntity"
      status="Estat**"
      :properties="itemProperties"
    />
    <h2 class="text-primary">CARD</h2>
    <CardDetail
      icon="users-viewfinder"
      title="DEALER, SUBDEALER I CARACTERÍSTIQUES VENDA"
      name=""
      description=""
      status=""
      :properties="dealerProperties"
    />
    <h2 class="text-primary">TABLA</h2>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ProfileDetailView'
}
</script>

<script setup lang="ts">
import CardDetail from '@/components/card/CardDetail.vue'
import { useHierarchyStore } from '@/stores/hierarchy'
import type { IHierarchyStore } from '@/stores/hierarchy/interfaces/store.interface'
import type { IHierarchyType } from '@/stores/hierarchy/interfaces/hierarchy-type.interface'
import type { ProfileResponse } from '@/services/api/hierarchy/interfaces/profile.interface'
import { formatDate } from '@/utils/format-date/formatDate'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const props = defineProps({
  hierarchyItemType: {
    type: String,
    required: true
  },
  hierarchyItemKey: {
    type: String,
    required: true
  }
})
const route = useRoute()
const hierarchyStore: IHierarchyStore = useHierarchyStore()

const itemId = computed(() => route.params.id)

const rolData = computed(() => hierarchyStore.itemById as ProfileResponse)
const titleEntity = computed(() => rolData.value?.name)
const descriptionEntity = computed(() => rolData.value?.description)
//
const company = computed(() => rolData.value?.company)
const usuariAD = computed(() => rolData.value?.user?.userAD)
const role = computed(() => rolData.value?.role)
const responsible = computed(() => rolData.value?.responsible)
const createdDate = computed(() => formatDate(rolData.value?.createdDate))
const lastModifiedDate = computed(() => formatDate(rolData.value?.lastModifiedDate))
const deactivationDate = computed(() =>
  rolData.value?.deactivationDate ? formatDate(rolData.value.deactivationDate) : null
)
//
const dealer = computed(() => rolData.value?.dealer)
const dealerCode = computed(() => rolData.value?.dealerCode)
const documentNumber = computed(() => rolData.value?.documentNumber)
const contact = computed(() => rolData.value?.name)
const contactEmail = computed(() => rolData.value?.user?.emailAddress)
const subDealer = computed(() => rolData.value?.subDealer)
const subDealerCode = computed(() => rolData.value?.subDealerCode)
const brand = computed(() => rolData.value?.company)
const channel = computed(() => rolData.value?.channel)

onMounted(() => {
  hierarchyStore.getItemById(props.hierarchyItemType as IHierarchyType, itemId.value as string)
})

const itemProperties = computed(() => [
  { label: 'Company', value: company.value, bold: true },
  { label: 'Usuari AD', value: usuariAD.value, bold: true },
  { label: 'Rol', value: role.value, bold: true },
  { label: 'Rol comercial', value: 'No**', bold: true },
  { label: 'Responsable', value: responsible.value, bold: true },
  { label: 'Data de creació', value: createdDate.value, bold: true },
  { label: 'Data de modificació', value: lastModifiedDate.value, bold: true },
  { label: 'Data de desactivació', value: deactivationDate.value, bold: true }
])

const dealerProperties = computed(() => [
  { label: 'Delaer', value: dealer.value, bold: true },
  { label: 'Codi Delaer', value: dealerCode.value, bold: true },
  { label: 'Nª Document', value: documentNumber.value, bold: true },
  { label: 'Persona conctacte', value: contact.value, bold: true },
  { label: 'Email Contacte', value: contactEmail.value, bold: true },
  { label: 'Sub Dealer', value: subDealer.value, bold: true },
  { label: 'Codi SubDealer', value: subDealerCode.value, bold: true },
  { label: 'Marca', value: brand.value, bold: true },
  { label: 'Canal', value: channel.value, bold: true }
])
</script>
