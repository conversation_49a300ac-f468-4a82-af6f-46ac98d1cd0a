import type { FieldConfig } from './interfaces/detail-field.interface'

const detailFieldKeys: Record<string, FieldConfig[]> = {
  general: [
    { label: 'ID', field: 'id' },
    { label: 'Nom', field: 'name' },
    { label: "URL de l'aplicació", field: 'appUrl' },
    { label: "URL de la imatge", field: 'imageUrl' }, 
    { label: 'Data de creació', field: 'createdDate' },
    { label: 'Creat per', field: 'createdById' },
    { label: 'Última modificació', field: 'lastModifiedDate' },
    { label: 'Modificat per', field: 'lastModifiedById' },
    { label: 'Versió', field: 'rowVersion' },
    { label: 'Data de desactivació', field: 'deactivationDate' }
  ]
}

export default detailFieldKeys
