<template>
  <div class="applications-detail h-full">
    <div class="flex items-center justify-between mb-6">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Detall de l'aplicació</h1>
    </div>

    <div class="space-y-6">
      <!-- Informació general -->
      <CardDetail
        icon="info-circle"
        title="Informació general"
        name="Detalls"
        :properties="generalFields"
        :image="'/src/assets/svg/web-search-cuate.svg'"
      />

      <!-- Rols associats -->
      <CardDetail
        icon="users"
        title="Rols associats"
        name="Rols"
        description="Llistat de rols vinculats a aquesta aplicació"
        :properties="roleFields"
      />
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ApplicationDetail'
}
</script>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import type { Ref } from 'vue'
import CardDetail from '@/components/card/CardDetail.vue'
import detailFieldKeys from '@/modules/applications/schema/detail/detailFieldKeys'

const props = defineProps({
  applicationId: {
    type: [String, Number],
    default: null
  },
  applicationData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['edit', 'delete', 'close'])

const applicationData: Ref<any> = ref({
  id: props.applicationId || 1,
  name: "Aplicació d'exemple",
  imageUrl: '',
  appUrl: '',
  roles: [],
  createdDate: new Date().toISOString(),
  createdById: '',
  lastModifiedDate: new Date().toISOString(),
  lastModifiedById: '',
  deactivationDate: null,
  rowVersion: new Date().toISOString(),
  ...props.applicationData
})

const formatDate = (dateString: string | null): string => {
  if (!dateString) return 'N/A'
  try {
    return new Date(dateString).toLocaleDateString('ca-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return 'Data no vàlida'
  }
}

const generalFields = computed(() => {
  return detailFieldKeys.general.map(({ label, field }) => ({
    label,
    value: field.includes('Date')
      ? formatDate(applicationData.value[field])
      : applicationData.value[field] || '-'
  }))
})

const roleFields = computed(() => {
  return applicationData.value.roles.length > 0
    ? applicationData.value.roles.map((role: any) => ({ label: 'Rol', value: role.name }))
    : [{ label: '', value: 'No hi ha rols associats a aquesta aplicació' }]
})
</script>

<style scoped>
.applications-detail {
  max-width: 100%;
  overflow-y: auto;
}
</style>
