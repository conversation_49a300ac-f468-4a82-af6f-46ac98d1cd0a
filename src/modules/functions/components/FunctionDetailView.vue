<template>
  <div class="h-full">
    <h2 class="text-primary">CARD</h2>
    <CardDetail
      icon="person-walking-dashed-line-arrow-right"
      title="DETALL FUNCIÓ"
      :name="titleEntity"
      :description="descriptionEntity"
      status="Estat**"
      :properties="itemProperties"
    />
    <h2 class="text-primary">TABLA</h2>
  </div>
</template>

<script lang="ts">
export default {
  name: 'FunctionDetailView'
}
</script>

<script setup lang="ts">
import CardDetail from '@/components/card/CardDetail.vue'
import { useHierarchyStore } from '@/stores/hierarchy'
import type { IHierarchyStore } from '@/stores/hierarchy/interfaces/store.interface'
import type { IHierarchyType } from '@/stores/hierarchy/interfaces/hierarchy-type.interface'
import { formatDate } from '@/utils/format-date/formatDate'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const props = defineProps({
  hierarchyItemType: {
    type: String,
    required: true
  },
  hierarchyItemKey: {
    type: String,
    required: true
  }
})
const route = useRoute()
const hierarchyStore: IHierarchyStore = useHierarchyStore()

const itemId = computed(() => route.params.id)

const rolData = computed(() => hierarchyStore.itemById)
const titleEntity = computed(() => rolData.value?.name)
const descriptionEntity = computed(() => rolData.value?.description)
const roleId = computed(() => rolData.value?.id)
const createdBy = computed(() => rolData.value?.createdById)
const createdDate = computed(() => formatDate(rolData.value?.createdDate))
const lastModifiedBy = computed(() => formatDate(rolData.value?.lastModifiedById))
const lastModifiedDate = computed(() => formatDate(rolData.value?.lastModifiedDate))
const deactivationDate = computed(() =>
  rolData.value?.deactivationDate ? formatDate(rolData.value.deactivationDate) : null
)

onMounted(() => {
  hierarchyStore.getItemById(props.hierarchyItemType as IHierarchyType, itemId.value as string)
})

const itemProperties = computed(() => [
  { label: 'Rol Id', value: roleId.value, bold: true },
  { label: 'Creada per', value: createdBy.value, bold: true },
  { label: 'Data de creació', value: createdDate.value, bold: true },
  { label: 'Modificada per', value: lastModifiedBy.value, bold: true },
  { label: 'Data de modificació', value: lastModifiedDate.value, bold: true },
  { label: 'Data de desactivació', value: deactivationDate.value, bold: true }
])
</script>
