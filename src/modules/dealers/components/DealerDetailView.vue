<template>
  <div class="h-full">
    <h2 class="text-primary">CARD</h2>
    <CardDetail
      icon="users-viewfinder"
      title="DEALER"
      :name="titleEntity"
      :description="descriptionEntity"
      status="Estat**"
      :properties="itemProperties"
    />
    <h2 class="text-primary">TABLA</h2>
  </div>
</template>

<script lang="ts">
export default {
  name: 'DealerDetailView'
}
</script>

<script setup lang="ts">
import CardDetail from '@/components/card/CardDetail.vue'
import { useHierarchyStore } from '@/stores/hierarchy'
import type { IHierarchyStore } from '@/stores/hierarchy/interfaces/store.interface'
import type { IHierarchyType } from '@/stores/hierarchy/interfaces/hierarchy-type.interface'
import type { DealerResponse } from '@/services/api/hierarchy/interfaces/dealer.interface'
import { formatDate } from '@/utils/format-date/formatDate'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const props = defineProps({
  hierarchyItemType: {
    type: String,
    required: true
  },
  hierarchyItemKey: {
    type: String,
    required: true
  }
})
const route = useRoute()
const hierarchyStore: IHierarchyStore = useHierarchyStore()

const itemId = computed(() => route.params.id)

const rolData = computed(() => hierarchyStore.itemById as DealerResponse)
const titleEntity = computed(() => rolData.value?.name)
const descriptionEntity = computed(() => rolData.value?.description)
//
const legalName = computed(() => rolData.value?.name)
const comercialCode = computed(() => rolData.value?.comercialCode)
const documentNumber = computed(() => rolData.value?.documentNumber)
const dealerType = computed(() => rolData.value?.dealerType)
const contactName = computed(() => rolData.value?.contactName)
const contactEmail = computed(() => rolData.value?.contactEmail)
const contactPhone = computed(() => rolData.value?.contactPhone)
const contactAddress = computed(() => rolData.value?.contactAddress)
const createdDate = computed(() => formatDate(rolData.value?.createdDate))
const lastModifiedDate = computed(() => formatDate(rolData.value?.lastModifiedDate))
const deactivationDate = computed(() =>
  rolData.value?.deactivationDate ? formatDate(rolData.value.deactivationDate) : null
)

onMounted(() => {
  hierarchyStore.getItemById(props.hierarchyItemType as IHierarchyType, itemId.value as string)
})

const itemProperties = computed(() => [
  { label: 'Nom Legal', value: legalName.value, bold: true },
  { label: 'Codi comercial', value: comercialCode.value, bold: true },
  { label: 'Número de document', value: documentNumber.value, bold: true },
  { label: 'Tipus de dealer', value: dealerType.value, bold: true },
  { label: 'Nom contacte', value: contactName.value, bold: true },
  { label: 'Email de contacte', value: contactEmail.value, bold: true },
  { label: 'Telèfon de contacte', value: contactPhone.value, bold: true },
  { label: 'Direcció', value: contactAddress.value, bold: true },
  { label: 'Data de creació', value: createdDate.value, bold: true },
  { label: 'Data de modificació', value: lastModifiedDate.value, bold: true },
  { label: 'Data de desactivaciós', value: deactivationDate.value, bold: true }
])
</script>
