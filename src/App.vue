<template>
  <Sidebar @sidebarPinnedChanged="handleSidebarPinnedChanged" />
  <main :class="sidebarPinned ? 'ml-[256px]' : 'ml-10'" class="h-full relative">
    <RouterView />
  </main>
  <div class="absolute bg-background-color -bottom-2 w-full h-16 z-10"></div>
</template>

<script setup lang="ts">
import { RouterView } from 'vue-router'
import { onMounted, ref } from 'vue'
import { defineUserTheme } from 'parlem-webcomponents-common'
import checkUserCredentials from '@/utils/user-credentials/userCredentials.ts'
import Sidebar from './components/sidebar/Sidebar.vue'
import { useHierarchyStore } from '@/stores/hierarchy'
import type { IHierarchyStore } from '@/stores/hierarchy/interfaces/store.interface'
import { FETCH_PICKLISTS } from '@/stores/hierarchy/constants/hierarchy.actions.constants'

const sidebarPinned = ref(false)
const hierarchyStore: IHierarchyStore = useHierarchyStore()

const handleSidebarPinnedChanged = (isPinned: boolean) => {
  sidebarPinned.value = isPinned
}

onMounted(async () => {
  defineUserTheme()
  try {
    await checkUserCredentials()
    await hierarchyStore[FETCH_PICKLISTS]()
  } catch (error) {
    console.error('Error:', error)
  }
})
</script>

<style scoped></style>
