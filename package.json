{"name": "parlem-user-management", "version": "0.0.2", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "prepare": "cypress install", "build:dev": "vue-tsc --build --watch", "test:dev": "vitest", "test:e2e": "start-server-and-test preview http://localhost:4173 'cypress run --e2e'", "test:e2e:dev": "start-server-and-test 'vite dev --port 4173' http://localhost:4173 'cypress open --e2e'", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@ag-grid-community/styles": "^32.3.5", "@azure/core-http": "^3.0.5", "@azure/msal-browser": "^4.13.0", "@azure/storage-blob": "^12.27.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "ag-grid-vue3": "^33.3.2", "axios": "^1.9.0", "parlem-webcomponents-common": "^1.1.242", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-i18n": "^11.1.5", "vue-router": "^4.5.0", "vue-toast-notification": "^3.1.3"}, "devDependencies": {"@tailwindcss/vite": "^4.1.10", "@tsconfig/node22": "^22.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitest/eslint-plugin": "^1.1.39", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "cypress": "^14.2.1", "eslint": "^9.22.0", "eslint-plugin-cypress": "^4.2.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "start-server-and-test": "^2.0.11", "tailwindcss": "^4.1.10", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}}