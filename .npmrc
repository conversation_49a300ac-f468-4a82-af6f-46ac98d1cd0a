use-node-version=19.8.1
registry=https://pkgs.dev.azure.com/ParlemTelecom/Parlem%20WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/
always-auth=true

; begin auth token
//pkgs.dev.azure.com/ParlemTelecom/Parlem%20WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/:username=ParlemTelecom
//pkgs.dev.azure.com/ParlemTelecom/Parlem%20WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/:_password=NGdxZW13aDNra3l2aWNzZDVncm92NWQ0anJxbmZkanBzaWh2Mmhha29hc2R1cmxiNjZicQ==
//pkgs.dev.azure.com/ParlemTelecom/Parlem%20WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/:email=<EMAIL>
//pkgs.dev.azure.com/ParlemTelecom/Parlem%20WebComponents/_packaging/ParlemTelecom-webcomponents/npm/:username=ParlemTelecom
//pkgs.dev.azure.com/ParlemTelecom/Parlem%20WebComponents/_packaging/ParlemTelecom-webcomponents/npm/:_password=NGdxZW13aDNra3l2aWNzZDVncm92NWQ0anJxbmZkanBzaWh2Mmhha29hc2R1cmxiNjZicQ==
//pkgs.dev.azure.com/ParlemTelecom/Parlem%20WebComponents/_packaging/ParlemTelecom-webcomponents/npm/:email=<EMAIL>
; end auth token